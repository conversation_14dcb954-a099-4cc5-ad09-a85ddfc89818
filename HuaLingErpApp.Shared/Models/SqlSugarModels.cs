using SqlSugar;
using System.ComponentModel.DataAnnotations;
using SqlSugar.DbConvert;

namespace HuaLingErpApp.Shared.Models {
    [SugarTable("po")]
    public class PurchaseOrder : IEntity<int> {
        [SugarColumn(ColumnName = "Id", IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        [SugarColumn(ColumnName = "po_num", ColumnDataType = "PonumType", IsNullable = true)]
        public string? PoNum { get; set; }

        [Required(ErrorMessage = "Vendor number is required")]
        [SugarColumn(ColumnName = "vendor_num", ColumnDataType = "VendnumType")]
        [StringLength(20, ErrorMessage = "Vendor number cannot exceed 20 characters")]
        public string? VendorNum { get; set; }

        [Required(ErrorMessage = "Currency code is required")]
        [SugarColumn(ColumnName = "curr_code", ColumnDataType = "CurrcodeType")]
        [Display(Name = "Currency Code")]
        [StringLength(3, ErrorMessage = "Currency code must be 3 characters")]
        public string? CurrencyCode { get; set; }

        [Required(ErrorMessage = "Status is required")]
        [SugarColumn(ColumnName = "stat", Length = 1, SqlParameterDbType = typeof(EnumToStringConvert))]
        [Display(Name = "Status")]
        public EnumStatus? Status { get; set; }

        [Required(ErrorMessage = "Terms code is required")]
        [SugarColumn(ColumnName = "terms_code", Length = 3)]
        [Display(Name = "Terms Code")]
        [StringLength(3, ErrorMessage = "Terms code must be 3 characters", MinimumLength = 3)]
        public string TermsCode { get; set; } = string.Empty;

        [Required(ErrorMessage = "Phone is required")]
        [SugarColumn(ColumnName = "phone", Length = 40)]
        [Display(Name = "Phone")]
        [Phone(ErrorMessage = "Please enter a valid phone number")]
        [StringLength(40, ErrorMessage = "Phone number cannot exceed 40 characters")]
        public string Phone { get; set; } = string.Empty;

        [Required(ErrorMessage = "Contact is required")]
        [SugarColumn(ColumnName = "contact", Length = 20)]
        [Display(Name = "Contact")]
        [StringLength(20, ErrorMessage = "Contact name cannot exceed 20 characters")]
        public string Contact { get; set; } = string.Empty;

        [Required(ErrorMessage = "Email is required")]
        [SugarColumn(ColumnName = "email", Length = 60)]
        [Display(Name = "Email")]
        [EmailAddress(ErrorMessage = "Please enter a valid email address")]
        [StringLength(60, ErrorMessage = "Email cannot exceed 60 characters")]
        public string Email { get; set; } = string.Empty;

        [Required(ErrorMessage = "Address is required")]
        [SugarColumn(ColumnName = "addr", Length = 60)]
        [Display(Name = "Address")]
        [StringLength(60, ErrorMessage = "Address cannot exceed 60 characters")]
        public string Address { get; set; } = string.Empty;

        [Required(ErrorMessage = "Purchase date is required")]
        [SugarColumn(ColumnName = "po_date")]
        [Display(Name = "Purchase Date")]
        [DataType(DataType.Date)]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd}", ApplyFormatInEditMode = true)]
        public DateTime? PoDate { get; set; }

        [Required(ErrorMessage = "Tax code is required")]
        [SugarColumn(ColumnName = "tax_code", Length = 3)]
        [Display(Name = "Tax Code")]
        [StringLength(3, ErrorMessage = "Tax code cannot exceed 3 characters", MinimumLength = 1)]
        public string TaxCode { get; set; } = string.Empty;

        [Required(ErrorMessage = "Tax rate is required")]
        [SugarColumn(ColumnName = "tax_rate", DecimalDigits = 4)]
        [Display(Name = "Tax Rate")]
        [Range(0, 100, ErrorMessage = "Tax rate must be between 0 and 100")]
        public decimal? TaxRate { get; set; }

        [SugarColumn(ColumnName = "exch_rate", ColumnDataType = "ExchrateType")]
        [Display(Name = "Exchange Rate")]
        [Range(0, double.MaxValue, ErrorMessage = "Exchange rate must be greater than 0")]
        public decimal? ExchangeRate { get; set; }

        public DateTime RecordDate { get; set; } = DateTime.Now;

        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public string CreatedBy { get; set; } = string.Empty;

        public string ModifiedBy { get; set; } = string.Empty;
    }

    [SugarTable("poitem")]
    public class PoItem : IEntity<int> {
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true, ColumnName = "Id")]
        public int Id { get; set; }

        [SugarColumn(ColumnName = "po_num", Length = 10, ColumnDataType = "PonumType", IsNullable = true)]
        [Display(Name = "PoNum")]
        public string? PoNum { get; set; }

        [Required(ErrorMessage = "PO line is required")]
        [SugarColumn(ColumnName = "po_line", IsNullable = false)]
        public short PoLine { get; set; }

        [Required(ErrorMessage = "Item is required")]
        [StringLength(30, ErrorMessage = "Item cannot exceed 30 characters")]
        [Display(Name = "Item")]
        [SugarColumn(ColumnName = "item", Length = 30, IsNullable = false)]
        public string Item { get; set; } = string.Empty;

        [SugarColumn(IsIgnore = true)] public string ItemDesc { get; set; } = string.Empty;

        [Required(ErrorMessage = "Quantity ordered is required")]
        [Display(Name = "Quantity Ordered")]
        [SugarColumn(ColumnName = "qty_ordered", DecimalDigits = 5, IsNullable = false)]
        public decimal QtyOrdered { get; set; }

        [Display(Name = "Quantity Received")]
        [SugarColumn(ColumnName = "qty_received", DecimalDigits = 5, DefaultValue = "0")]
        public decimal QtyReceived { get; set; }

        [Display(Name = "Quantity Voucher")]
        [SugarColumn(ColumnName = "qty_voucher", DecimalDigits = 5, DefaultValue = "0")]
        public decimal QtyVoucher { get; set; }

        [Required(ErrorMessage = "Unit cost is required")]
        [Display(Name = "Unit Cost")]
        [SugarColumn(ColumnName = "unit_cost", DecimalDigits = 8, DefaultValue = "0", IsNullable = false)]
        public decimal UnitCost { get; set; }

        [Required(ErrorMessage = "Status is required")]
        [Display(Name = "Status")]
        [SugarColumn(ColumnName = "stat", Length = 1, SqlParameterDbType = typeof(EnumToStringConvert))]
        public EnumStatus? Status { get; set; }

        [Required(ErrorMessage = "Due date is required")]
        [Display(Name = "Due Date")]
        [DataType(DataType.Date)]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd}", ApplyFormatInEditMode = true)]
        [SugarColumn(ColumnName = "due_date", IsNullable = false)]
        public DateTime DueDate { get; set; } = DateTime.Today;

        [Display(Name = "Unit Cost Received")]
        [SugarColumn(ColumnName = "unit_cost_received", DecimalDigits = 8, DefaultValue = "0")]
        public decimal UnitCostReceived { get; set; }

        [Required(ErrorMessage = "Location is required")]
        [StringLength(10, ErrorMessage = "Location cannot exceed 10 characters")]
        [SugarColumn(ColumnName = "loc", Length = 10, IsNullable = false)]
        public string Location { get; set; } = string.Empty;

        [Required(ErrorMessage = "Unit of measure is required")]
        [Display(Name = "U/M")]
        [StringLength(3, ErrorMessage = "Unit of measure cannot exceed 3 characters")]
        [SugarColumn(ColumnName = "u_m", Length = 3, IsNullable = false)]
        public string UnitOfMeasure { get; set; } = string.Empty;


        public DateTime RecordDate { get; set; } = DateTime.Now;

        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public string CreatedBy { get; set; } = string.Empty;

        public string ModifiedBy { get; set; } = string.Empty;
    }


    [SugarTable("vendor")]
    public class Vendor : IEntity<int> {
        [SugarColumn(ColumnName = "id", IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        [Required(ErrorMessage = "Vendor number is required")]
        [StringLength(10, ErrorMessage = "Vendor number cannot exceed 10 characters")]
        [SugarColumn(ColumnName = "vend_num", Length = 10, IsNullable = false)]
        public string VendNum { get; set; } = string.Empty;

        [StringLength(60, ErrorMessage = "Name cannot exceed 60 characters")]
        [SugarColumn(ColumnName = "name", Length = 60, IsNullable = true)]
        public string? Name { get; set; }

        [StringLength(3, ErrorMessage = "Currency code must be 3 characters", MinimumLength = 3)]
        [SugarColumn(ColumnName = "curr_code", Length = 3, IsNullable = true)]
        public string? CurrencyCode { get; set; }

        [StringLength(3, ErrorMessage = "Terms code must be 3 characters", MinimumLength = 3)]
        [SugarColumn(ColumnName = "terms_code", Length = 3, IsNullable = true)]
        public string? TermsCode { get; set; }

        [StringLength(3, ErrorMessage = "Tax code must be 3 characters", MinimumLength = 3)]
        [SugarColumn(ColumnName = "tax_code", Length = 3, IsNullable = true)]
        public string? TaxCode { get; set; }

        [StringLength(20, ErrorMessage = "Contact name cannot exceed 20 characters")]
        [SugarColumn(ColumnName = "contact", Length = 20, IsNullable = true)]
        public string? Contact { get; set; }

        [StringLength(40, ErrorMessage = "Phone number cannot exceed 40 characters")]
        [Phone(ErrorMessage = "Please enter a valid phone number")]
        [SugarColumn(ColumnName = "phone", Length = 40, IsNullable = true)]
        public string? Phone { get; set; }

        [StringLength(60, ErrorMessage = "Email cannot exceed 60 characters")]
        [EmailAddress(ErrorMessage = "Please enter a valid email address")]
        [SugarColumn(ColumnName = "email", Length = 60, IsNullable = true)]
        public string? Email { get; set; }

        [StringLength(60, ErrorMessage = "Address cannot exceed 60 characters")]
        [SugarColumn(ColumnName = "addr", Length = 60, IsNullable = true)]
        public string? Address { get; set; }

        [SugarColumn(ColumnName = "CreatedDate", IsNullable = true)]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        public string CreatedBy { get; set; } = string.Empty;

        public string ModifiedBy { get; set; } = string.Empty;

        [SugarColumn(ColumnName = "RecordDate", IsNullable = true)]
        public DateTime RecordDate { get; set; } = DateTime.Now;
    }

    [SugarTable("currencycode")]
    public class CurrencyCode : IEntity<string> {
        [SugarColumn(ColumnName = "curr_code", IsPrimaryKey = true, Length = 3)]
        [Required(ErrorMessage = "Currency code is required")]
        [StringLength(3, ErrorMessage = "Currency code cannot exceed 3 characters")]
        public string Id { get; set; } = string.Empty;

        [SugarColumn(ColumnName = "description", Length = 30)]
        [StringLength(30, ErrorMessage = "Description cannot exceed 30 characters")]
        public string? Description { get; set; }

        [SugarColumn(ColumnName = "CreatedDate", IsNullable = true)]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        public string CreatedBy { get; set; } = string.Empty;

        public string ModifiedBy { get; set; } = string.Empty;


        [SugarColumn(ColumnName = "RecordDate", IsNullable = true)]
        public DateTime RecordDate { get; set; } = DateTime.Now;
    }

    [SugarTable("termscode")]
    public class TermsCode : IEntity<string> {
        [SugarColumn(ColumnName = "terms_code", IsPrimaryKey = true, Length = 3)]
        [Required(ErrorMessage = "Terms code is required")]
        [StringLength(3, MinimumLength = 1, ErrorMessage = "Terms code cannot exceed 3 characters")]
        public string Id { get; set; } = string.Empty;

        [SugarColumn(ColumnName = "description", Length = 30)]
        [StringLength(30, ErrorMessage = "Description cannot exceed 30 characters")]
        public string? Description { get; set; }

        [SugarColumn(ColumnName = "type")]
        [StringLength(50, ErrorMessage = "Type cannot exceed 50 characters")]
        public string? Type { get; set; }

        [SugarColumn(ColumnName = "CreatedDate", IsNullable = true)]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        public string CreatedBy { get; set; } = string.Empty;

        public string ModifiedBy { get; set; } = string.Empty;

        [SugarColumn(ColumnName = "RecordDate", IsNullable = true)]
        public DateTime RecordDate { get; set; } = DateTime.Now;
    }

    [SugarTable("taxcode")]
    public class TaxCode : IEntity<string> {
        [SugarColumn(ColumnName = "tax_code", IsPrimaryKey = true, Length = 3)]
        [Required(ErrorMessage = "Tax code is required")]
        [StringLength(3, MinimumLength = 1, ErrorMessage = "Tax code cannot exceed 3 characters")]
        public string Id { get; set; } = string.Empty;

        [Required(ErrorMessage = "Tax rate is required")]
        [SugarColumn(ColumnName = "tax_rate", DecimalDigits = 4)]
        [Display(Name = "Tax Rate")]
        [Range(0, 100, ErrorMessage = "Tax rate must be between 0 and 100")]
        public decimal? TaxRate { get; set; }

        [SugarColumn(ColumnName = "CreatedDate", IsNullable = true)]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        public string CreatedBy { get; set; } = string.Empty;

        public string ModifiedBy { get; set; } = string.Empty;

        [SugarColumn(ColumnName = "RecordDate", IsNullable = true)]
        public DateTime RecordDate { get; set; } = DateTime.Now;
    }

    [SugarTable("item")]
    public class Item : IEntity<string> {
        [Display(Name = "Item Code")]
        [Required(ErrorMessage = "Item code is required")]
        [StringLength(30, ErrorMessage = "Item code must not exceed {1} characters")]
        [SugarColumn(ColumnName = "item", ColumnDataType = "ItemType", IsPrimaryKey = true, IsNullable = false)]
        public string Id { get; set; } = string.Empty;


        [Display(Name = "Description")]
        [StringLength(60, ErrorMessage = "Description must not exceed {1} characters")]
        [SugarColumn(ColumnName = "description", ColumnDataType = "DescriptionType")]
        public string Description { get; set; } = string.Empty;


        [Display(Name = "Family Code")]
        [StringLength(15, ErrorMessage = "Family code must not exceed {1} characters")]
        [SugarColumn(ColumnName = "family_code", ColumnDataType = "FamilycodeType")]
        public string FamilyCode { get; set; } = string.Empty;


        [Display(Name = "Unit of Measure")]
        [StringLength(3, ErrorMessage = "UOM must not exceed {1} characters")]
        [SugarColumn(ColumnName = "u_m", ColumnDataType = "UmType")]
        public string UnitOfMeasure { get; set; } = string.Empty;


        [Display(Name = "Material Type")]
        [StringLength(10, ErrorMessage = "Material type must not exceed {1} characters")]
        [SugarColumn(ColumnName = "matltype", ColumnDataType = "nvarchar(10)")]
        public string MaterialType { get; set; } = string.Empty;


        [Display(Name = "PMT Code")]
        [StringLength(10, ErrorMessage = "PMT code must not exceed {1} characters")]
        [SugarColumn(ColumnName = "p_m_t_code", ColumnDataType = "nvarchar(10)")]
        public string PMTCode { get; set; } = string.Empty;


        [Display(Name = "ABC Code")]
        [StringLength(3, ErrorMessage = "ABC code must not exceed {1} characters")]
        [SugarColumn(ColumnName = "abc_code", ColumnDataType = "nvarchar(3)")]
        public string ABCCode { get; set; } = string.Empty;


        [Display(Name = "Unit Cost")]
        [Range(0, 999999999.99999999, ErrorMessage = "Unit cost must be between {1} and {2}")]
        [SugarColumn(ColumnName = "unit_cost", ColumnDataType = "UnitcostType")]
        public decimal? UnitCost { get; set; }


        [Display(Name = "Unit Price")]
        [Range(0, 999999999.99, ErrorMessage = "Unit price must be between {1} and {2}")]
        [SugarColumn(ColumnName = "unit_price", ColumnDataType = "AmountType")]
        public decimal? UnitPrice { get; set; }


        [Display(Name = "Status")]
        [StringLength(10, ErrorMessage = "Status must not exceed {1} characters")]
        [SugarColumn(ColumnName = "stat", ColumnDataType = "nvarchar(10)")]
        public string Status { get; set; } = string.Empty;


        [Display(Name = "Lot Tracked")]
        [SugarColumn(ColumnName = "LotTracked")]
        public bool? IsLotTracked { get; set; }


        [Display(Name = "Lot Format")]
        [StringLength(20, ErrorMessage = "Lot format must not exceed {1} characters")]
        [SugarColumn(ColumnName = "LotFormat", ColumnDataType = "nvarchar(20)")]
        public string LotFormat { get; set; } = string.Empty;

        [Display(Name = "Create Date")]
        [SugarColumn(ColumnName = "CreateDate")]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        public string CreatedBy { get; set; } = string.Empty;

        public string ModifiedBy { get; set; } = string.Empty;

        [Display(Name = "Record Date")]
        [SugarColumn(ColumnName = "RecordDate")]
        public DateTime RecordDate { get; set; } = DateTime.Now;
    }

    [SugarTable("loc")]
    public class Location : IEntity<string> {
        [Display(Name = "Location Code")]
        [Required(ErrorMessage = "Location code is required")]
        [StringLength(10, ErrorMessage = "Location code must not exceed {1} characters")]
        [SugarColumn(ColumnName = "loc", IsPrimaryKey = true, IsNullable = false, Length = 10)]
        public string Id { get; set; } = string.Empty;

        [Display(Name = "Description")]
        [Required(ErrorMessage = "Description is required")]
        [StringLength(20, ErrorMessage = "Description must not exceed {1} characters")]
        [SugarColumn(ColumnName = "description", IsNullable = false, Length = 20)]
        public string Description { get; set; } = string.Empty;

        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public string CreatedBy { get; set; } = string.Empty;

        public string ModifiedBy { get; set; } = string.Empty;


        public DateTime RecordDate { get; set; } = DateTime.Now;
    }

    [SugarTable("itemloc")]
    public class ItemLocation : IEntity<int> {
        [SugarColumn(ColumnName = "id", IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        [Display(Name = "Item Code")]
        [Required(ErrorMessage = "Item code is required")]
        [StringLength(30, ErrorMessage = "Item code must not exceed {1} characters")]
        [SugarColumn(ColumnName = "item", ColumnDataType = "ItemType", IsNullable = false)]
        public string Item { get; set; } = string.Empty;

        [Display(Name = "Location")]
        [Required(ErrorMessage = "Location is required")]
        [StringLength(10, ErrorMessage = "Location code must not exceed {1} characters")]
        [SugarColumn(ColumnName = "loc", ColumnDataType = "locType", IsNullable = false)]
        public string Location { get; set; } = string.Empty;

        [Display(Name = "Account")]
        [StringLength(8, ErrorMessage = "Account code must not exceed {1} characters")]
        [SugarColumn(ColumnName = "acct", ColumnDataType = "AcctType")]
        public string? Account { get; set; }

        [Display(Name = "Quantity On Hand")]
        [SugarColumn(ColumnName = "qty_on_hand", ColumnDataType = "QtyType", DefaultValue = "0")]
        public decimal QuantityOnHand { get; set; }

        [Display(Name = "Rank")]
        [SugarColumn(ColumnName = "rank")]
        public byte? Rank { get; set; }

        [Display(Name = "Unit of Measure")]
        [StringLength(3, ErrorMessage = "UOM must not exceed {1} characters")]
        [SugarColumn(ColumnName = "u_m", ColumnDataType = "UmType")]
        public string? UnitOfMeasure { get; set; }

        [Display(Name = "Account Unit 1")]
        [StringLength(4, ErrorMessage = "Account unit 1 must not exceed {1} characters")]
        [SugarColumn(ColumnName = "acct_unit1", ColumnDataType = "UnitcdType")]
        public string? AccountUnit1 { get; set; }

        [Display(Name = "Account Unit 2")]
        [StringLength(4, ErrorMessage = "Account unit 2 must not exceed {1} characters")]
        [SugarColumn(ColumnName = "acct_unit2", ColumnDataType = "UnitcdType")]
        public string? AccountUnit2 { get; set; }


        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public string CreatedBy { get; set; } = string.Empty;

        public string ModifiedBy { get; set; } = string.Empty;


        public DateTime RecordDate { get; set; } = DateTime.Now;
    }

    [SugarTable("UM")]
    public class UnitOfMeasure : IEntity<string> {
        [Display(Name = "U/M")]
        [Required(ErrorMessage = "Unit of measure code is required")]
        [StringLength(10, ErrorMessage = "Unit of measure code must not exceed {1} characters")]
        [SugarColumn(ColumnName = "u_m", IsPrimaryKey = true, IsNullable = false, Length = 10)]
        public string Id { get; set; } = string.Empty;

        public DateTime RecordDate { get; set; } = DateTime.Now;
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public string CreatedBy { get; set; } = string.Empty;

        public string ModifiedBy { get; set; } = string.Empty;

        [Display(Name = "Description")]
        [StringLength(25, ErrorMessage = "Description must not exceed {1} characters")]
        [SugarColumn(ColumnName = "description", Length = 25)]
        public string? Description { get; set; }
    }

    [SugarTable("reasoncode")]
    public class ReasonCode : IEntity<int> {
        [SugarColumn(ColumnName = "Id", IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        [Required(ErrorMessage = "Reason code is required")]
        [SugarColumn(ColumnName = "reason_code", ColumnDataType = "ReasoncodeType", IsNullable = false)]
        [StringLength(3, ErrorMessage = "Reason code cannot exceed 3 characters")]
        public string ReasonCodeValue { get; set; } = string.Empty;

        /// <summary>
        /// Description
        /// </summary>
        [SugarColumn(ColumnName = "description", ColumnDataType = "DescriptionType", IsNullable = true)]
        [StringLength(60, ErrorMessage = "Description cannot exceed 60 characters")]
        public string? Description { get; set; }

        /// <summary>
        /// Reason Class
        /// </summary>
        [Required(ErrorMessage = "Reason class is required")]
        [SugarColumn(ColumnName = "reason_class", Length = 15, IsNullable = false)]
        [StringLength(15, ErrorMessage = "Reason class cannot exceed 15 characters")]
        public string ReasonClass { get; set; } = string.Empty;

        /// <summary>
        /// Account
        /// </summary>
        [SugarColumn(ColumnName = "acct", ColumnDataType = "AcctType", IsNullable = true)]
        [StringLength(8, ErrorMessage = "Account cannot exceed 8 characters")]
        public string? Account { get; set; }

        /// <summary>
        /// Account Unit 1
        /// </summary>
        [SugarColumn(ColumnName = "acct_unit1", ColumnDataType = "UnitcdType", IsNullable = true)]
        [StringLength(4, ErrorMessage = "Account unit 1 cannot exceed 4 characters")]
        public string? AccountUnit1 { get; set; }

        /// <summary>
        /// Account Unit 2
        /// </summary>
        [SugarColumn(ColumnName = "acct_unit2", ColumnDataType = "UnitcdType", IsNullable = true)]
        [StringLength(4, ErrorMessage = "Account unit 2 cannot exceed 4 characters")]
        public string? AccountUnit2 { get; set; }

        // IEntity implementation
        [SugarColumn(IsOnlyIgnoreUpdate = true)]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        public string CreatedBy { get; set; } = string.Empty;

        public string ModifiedBy { get; set; } = string.Empty;

        public DateTime RecordDate { get; set; } = DateTime.Now;
    }

    public class PurchaseOrderReceivingDto {
        [Required(ErrorMessage = "PO Number is required")]
        [Display(Name = "PO Number")]
        public string PoNum { get; set; } = string.Empty;

        [Required(ErrorMessage = "Line item number is required")]
        [Display(Name = "PO Line")]
        public int PoLine { get; set; }

        [Required(ErrorMessage = "Item number is required")]
        [Display(Name = "Item")]
        public string Item { get; set; } = string.Empty;

        [Display(Name = "Location")]
        [Required(ErrorMessage = "Location is required")]
        [StringLength(10, ErrorMessage = "Location code must not exceed {1} characters")]
        public string Location { get; set; } = string.Empty;

        [Display(Name = "Unit of Measure")]
        [StringLength(3, ErrorMessage = "UOM must not exceed {1} characters")]
        public string? UnitOfMeasure { get; set; }

        [Display(Name = "Ordered Qty")] public decimal QtyOrdered { get; set; }

        [Display(Name = "Received Qty")] public decimal QtyReceived { get; set; }

        [Display(Name = "Receiving Qty")] public decimal QtyReceiving { get; set; }

        [Display(Name = "Reason Code")] public string? ReasonCode { get; set; }

        [Display(Name = "Info")] public string? Info { get; set; }

        [Display(Name = "Transaction Date")] public DateTime? TransDate { get; set; }
    }

    public class ItemInventoryDto {
        public Item? Item { get; set; } = new();
        public string Location { get; set; } = string.Empty;
        [Display(Name = "Qty On Hand")] public decimal QuantityOnHand { get; set; }
    }

    public enum EnumStatus {
        [Display(Name = "Planning")] P,
        [Display(Name = "Ordered")] O,
        [Display(Name = "Completed")] C,
    }

    [SugarTable("parms")]
    public class Parms : IEntity<int> {
        [SugarColumn(ColumnName = "Id", IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        [SugarColumn(ColumnName = "base_curr_code", ColumnDataType = "nvarchar(3)", IsNullable = true)]
        [Display(Name = "Base Currency Code")]
        [StringLength(3, ErrorMessage = "Base currency code cannot exceed 3 characters")]
        public string? BaseCurrCode { get; set; }

        [SugarColumn(ColumnName = "payable_acct", ColumnDataType = "AcctType", IsNullable = true)]
        [Display(Name = "Payable Account")]
        public string? PayableAcct { get; set; }

        [SugarColumn(ColumnName = "payable_acct_unit1", ColumnDataType = "UnitcdType", IsNullable = true)]
        [Display(Name = "Payable Account Unit 1")]
        public string? PayableAcctUnit1 { get; set; }

        [SugarColumn(ColumnName = "payable_acct_unit2", ColumnDataType = "UnitcdType", IsNullable = true)]
        [Display(Name = "Payable Account Unit 2")]
        public string? PayableAcctUnit2 { get; set; }

        [SugarColumn(ColumnName = "pur_acct", ColumnDataType = "AcctType", IsNullable = true)]
        [Display(Name = "Purchase Account")]
        public string? PurAcct { get; set; }

        [SugarColumn(ColumnName = "pur_acct_unit1", ColumnDataType = "UnitcdType", IsNullable = true)]
        [Display(Name = "Purchase Account Unit 1")]
        public string? PurAcctUnit1 { get; set; }

        [SugarColumn(ColumnName = "pur_acct_unit2", ColumnDataType = "UnitcdType", IsNullable = true)]
        [Display(Name = "Purchase Account Unit 2")]
        public string? PurAcctUnit2 { get; set; }

        [SugarColumn(ColumnName = "pur_misc_acct", ColumnDataType = "AcctType", IsNullable = true)]
        [Display(Name = "Purchase Miscellaneous Account")]
        public string? PurMiscAcct { get; set; }

        [SugarColumn(ColumnName = "pur_misc_acct_unit1", ColumnDataType = "UnitcdType", IsNullable = true)]
        [Display(Name = "Purchase Miscellaneous Account Unit 1")]
        public string? PurMiscAcctUnit1 { get; set; }

        [SugarColumn(ColumnName = "pur_misc_acct_unit2", ColumnDataType = "UnitcdType", IsNullable = true)]
        [Display(Name = "Purchase Miscellaneous Account Unit 2")]
        public string? PurMiscAcctUnit2 { get; set; }

        [SugarColumn(ColumnName = "input_tax_acct", ColumnDataType = "AcctType", IsNullable = true)]
        [Display(Name = "Input Tax Account")]
        public string? InputTaxAcct { get; set; }

        [SugarColumn(ColumnName = "input_tax_acct_unti1", ColumnDataType = "UnitcdType", IsNullable = true)]
        [Display(Name = "Input Tax Account Unit 1")]
        public string? InputTaxAcctUnit1 { get; set; }

        [SugarColumn(ColumnName = "input_tax_acct_unti2", ColumnDataType = "UnitcdType", IsNullable = true)]
        [Display(Name = "Input Tax Account Unit 2")]
        public string? InputTaxAcctUnit2 { get; set; }

        [SugarColumn(ColumnName = "pur_expense_acct", ColumnDataType = "AcctType", IsNullable = true)]
        [Display(Name = "Purchase Expense Account")]
        public string? PurExpenseAcct { get; set; }

        [SugarColumn(ColumnName = "pur_expense_acct_unit1", ColumnDataType = "UnitcdType", IsNullable = true)]
        [Display(Name = "Purchase Expense Account Unit 1")]
        public string? PurExpenseAcctUnit1 { get; set; }

        [SugarColumn(ColumnName = "pur_expense_acct_unit2", ColumnDataType = "UnitcdType", IsNullable = true)]
        [Display(Name = "Purchase Expense Account Unit 2")]
        public string? PurExpenseAcctUnit2 { get; set; }

        [SugarColumn(ColumnName = "cgs_acct", ColumnDataType = "AcctType", IsNullable = true)]
        [Display(Name = "Cost of Goods Sold Account")]
        public string? CgsAcct { get; set; }

        [SugarColumn(ColumnName = "cgs_acct_unit1", ColumnDataType = "UnitcdType", IsNullable = true)]
        [Display(Name = "Cost of Goods Sold Account Unit 1")]
        public string? CgsAcctUnit1 { get; set; }

        [SugarColumn(ColumnName = "cgs_acct_unit2", ColumnDataType = "UnitcdType", IsNullable = true)]
        [Display(Name = "Cost of Goods Sold Account Unit 2")]
        public string? CgsAcctUnit2 { get; set; }

        [SugarColumn(ColumnName = "po_order_prefix", ColumnDataType = "nvarchar(10)", IsNullable = true)]
        [Display(Name = "Purchase Order Prefix")]
        [StringLength(10, ErrorMessage = "PO order prefix cannot exceed 10 characters")]
        public string? PoOrderPrefix { get; set; }

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        public string CreatedBy { get; set; } = string.Empty;

        public string ModifiedBy { get; set; } = string.Empty;

        public DateTime RecordDate { get; set; } = DateTime.Now;
    }
 [SugarTable("customer")]
    public class Customer : IEntity<int> {
        [SugarColumn(ColumnName = "id", IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        [SugarColumn(ColumnName = "cust_num", Length = 10, IsNullable = false)]
        [Required(ErrorMessage = "Customer number is required")]
        [StringLength(10, ErrorMessage = "Customer number cannot exceed 10 characters")]
        [Display(Name = "Customer Number")]
        public string CustNum { get; set; } = string.Empty;

        [SugarColumn(ColumnName = "name", Length = 60, IsNullable = true)]
        [StringLength(60, ErrorMessage = "Name cannot exceed 60 characters")]
        [Display(Name = "Customer Name")]
        public string? Name { get; set; }

        [SugarColumn(ColumnName = "curr_code", Length = 3, IsNullable = true)]
        [StringLength(3, ErrorMessage = "Currency code cannot exceed 3 characters")]
        [Display(Name = "Currency Code")]
        public string? CurrCode { get; set; }

        [SugarColumn(ColumnName = "terms_code", Length = 3, IsNullable = true)]
        [StringLength(3, ErrorMessage = "Terms code cannot exceed 3 characters")]
        [Display(Name = "Terms Code")]
        public string? TermsCode { get; set; }

        [SugarColumn(ColumnName = "tax_code", Length = 3, IsNullable = true)]
        [StringLength(3, ErrorMessage = "Tax code cannot exceed 3 characters")]
        [Display(Name = "Tax Code")]
        public string? TaxCode { get; set; }

        [SugarColumn(ColumnName = "contact", Length = 20, IsNullable = true)]
        [StringLength(20, ErrorMessage = "Contact cannot exceed 20 characters")]
        [Display(Name = "Contact")]
        public string? Contact { get; set; }

        [SugarColumn(ColumnName = "phone", Length = 40, IsNullable = true)]
        [StringLength(40, ErrorMessage = "Phone cannot exceed 40 characters")]
        [Display(Name = "Phone")]
        public string? Phone { get; set; }

        [SugarColumn(ColumnName = "email", Length = 60, IsNullable = true)]
        [StringLength(60, ErrorMessage = "Email cannot exceed 60 characters")]
        [Display(Name = "Email")]
        [EmailAddress(ErrorMessage = "Invalid email format")]
        public string? Email { get; set; }

        [SugarColumn(ColumnName = "addr", Length = 60, IsNullable = true)]
        [StringLength(60, ErrorMessage = "Address cannot exceed 60 characters")]
        [Display(Name = "Address")]
        public string? Addr { get; set; }

        [SugarColumn(ColumnName = "CreatedDate", IsNullable = true)]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        [SugarColumn(ColumnName = "RecordDate", IsNullable = true)]
        public DateTime RecordDate { get; set; } = DateTime.Now;

        [SugarColumn(ColumnName = "CreatedBy", Length = 40, IsNullable = true)]
        [StringLength(20, ErrorMessage = "Created by cannot exceed 40 characters")]
        public string CreatedBy { get; set; } = string.Empty;

        [SugarColumn(ColumnName = "ModifiedBy", Length = 40, IsNullable = true)]
        [StringLength(20, ErrorMessage = "Modified by cannot exceed 40 characters")]
        public string ModifiedBy { get; set; } = string.Empty;
    }

    [SugarTable("co")]
    public class CustomerOrder : IEntity<int>
    {
        [SugarColumn(ColumnName = "id", IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        [SugarColumn(ColumnName = "co_num", ColumnDataType = "PonumType", IsNullable = false)]
        [Display(Name = "CO Number")]
        public string? CoNum { get; set; }

        [SugarColumn(ColumnName = "cust_num", ColumnDataType = "VendnumType", IsNullable = true)]
        [StringLength(20, ErrorMessage = "Customer number cannot exceed 20 characters")]
        [Display(Name = "Customer Number")]
        public string? CustNum { get; set; }

        [SugarColumn(ColumnName = "curr_code", ColumnDataType = "CurrcodeType", IsNullable = true)]
        [StringLength(3, ErrorMessage = "Currency code must be 3 characters")]
        [Display(Name = "Currency Code")]
        public string? CurrCode { get; set; }

        [SugarColumn(ColumnName = "terms_code", Length = 3, IsNullable = true)]
        [StringLength(3, ErrorMessage = "Terms code must be 3 characters")]
        [Display(Name = "Terms Code")]
        public string? TermsCode { get; set; }

        [SugarColumn(ColumnName = "tax_code", Length = 3, IsNullable = true)]
        [StringLength(3, ErrorMessage = "Tax code must be 3 characters")]
        [Display(Name = "Tax Code")]
        public string? TaxCode { get; set; }

        [SugarColumn(ColumnName = "contact", Length = 20, IsNullable = true)]
        [StringLength(20, ErrorMessage = "Contact cannot exceed 20 characters")]
        [Display(Name = "Contact")]
        public string? Contact { get; set; }

        [SugarColumn(ColumnName = "phone", Length = 40, IsNullable = true)]
        [StringLength(40, ErrorMessage = "Phone cannot exceed 40 characters")]
        [Display(Name = "Phone")]
        public string? Phone { get; set; }

        [SugarColumn(ColumnName = "email", Length = 60, IsNullable = true)]
        [StringLength(60, ErrorMessage = "Email cannot exceed 60 characters")]
        [Display(Name = "Email")]
        [EmailAddress(ErrorMessage = "Invalid email format")]
        public string? Email { get; set; }

        [SugarColumn(ColumnName = "addr", Length = 60, IsNullable = true)]
        [StringLength(60, ErrorMessage = "Address cannot exceed 60 characters")]
        [Display(Name = "Address")]
        public string? Addr { get; set; }

        [SugarColumn(ColumnName = "co_date", IsNullable = true)]
        [Display(Name = "CO Date")]
        [DataType(DataType.Date)]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd}", ApplyFormatInEditMode = true)]
        public DateTime? CoDate { get; set; } = DateTime.Today;

        [SugarColumn(ColumnName = "stat", Length = 1, SqlParameterDbType = typeof(EnumToStringConvert), IsNullable = true)]
        [Display(Name = "Status")]
        public EnumStatus? Status { get; set; }

        [Required(ErrorMessage = "Tax rate is required")]
        [SugarColumn(ColumnName = "tax_rate", DecimalDigits = 4)]
        [Display(Name = "Tax Rate")]
        [Range(0, 100, ErrorMessage = "Tax rate must be between 0 and 100")]
        public decimal? TaxRate { get; set; }

        [SugarColumn(ColumnName = "exch_rate", ColumnDataType = "ExchrateType", IsNullable = true)]
        [Display(Name = "Exchange Rate")]
        [Range(0, double.MaxValue, ErrorMessage = "Exchange rate must be greater than 0")]
        public decimal? ExchRate { get; set; }

        [SugarColumn(ColumnName = "CreatedDate", IsNullable = true)]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        [SugarColumn(ColumnName = "RecordDate", IsNullable = true)]
        public DateTime RecordDate { get; set; } = DateTime.Now;

        [SugarColumn(ColumnName = "CreatedBy", Length = 40, IsNullable = true)]
        [StringLength(40, ErrorMessage = "Created by cannot exceed 40 characters")]
        public string CreatedBy { get; set; } = string.Empty;

        [SugarColumn(ColumnName = "ModifiedBy", Length = 40, IsNullable = true)]
        [StringLength(40, ErrorMessage = "Modified by cannot exceed 40 characters")]
        public string ModifiedBy { get; set; } = string.Empty;
    }

    [SugarTable("coitem")]
    public class CoItem : IEntity<int>
    {
        [SugarColumn(ColumnName = "id", IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        [SugarColumn(ColumnName = "co_num", Length = 10, IsNullable = false)]
        [Required(ErrorMessage = "CO number is required")]
        [StringLength(10, ErrorMessage = "CO number cannot exceed 10 characters")]
        [Display(Name = "CO Number")]
        public string CoNum { get; set; } = string.Empty;

        [SugarColumn(ColumnName = "co_line", IsNullable = false)]
        [Required(ErrorMessage = "CO line is required")]
        [Display(Name = "CO Line")]
        public short CoLine { get; set; }

        [SugarColumn(ColumnName = "item", ColumnDataType = "ItemType", IsNullable = true)]
        [StringLength(30, ErrorMessage = "Item cannot exceed 30 characters")]
        [Display(Name = "Item")]
        public string? Item { get; set; }

        [SugarColumn(ColumnName = "qty_ordered", ColumnDataType = "QtyType", IsNullable = true)]
        [Display(Name = "Qty Ordered")]
        [Range(0, double.MaxValue, ErrorMessage = "Quantity ordered must be greater than or equal to 0")]
        public decimal QtyOrdered { get; set; } = 0;

        [SugarColumn(ColumnName = "qty_shipped", ColumnDataType = "QtyType", IsNullable = true)]
        [Display(Name = "Qty Shipped")]
        [Range(0, double.MaxValue, ErrorMessage = "Quantity shipped must be greater than or equal to 0")]
        public decimal QtyShipped { get; set; } = 0;

        [SugarColumn(ColumnName = "qty_invoiced", ColumnDataType = "QtyType", IsNullable = true)]
        [Display(Name = "Qty Invoiced")]
        [Range(0, double.MaxValue, ErrorMessage = "Quantity invoiced must be greater than or equal to 0")]
        public decimal QtyInvoiced { get; set; } = 0;

        [SugarColumn(ColumnName = "unit_price", ColumnDataType = "UnitcostType", IsNullable = true)]
        [Display(Name = "Unit Price")]
        [Range(0, double.MaxValue, ErrorMessage = "Unit price must be greater than or equal to 0")]
        public decimal UnitPrice { get; set; } = 0;

        [SugarColumn(ColumnName = "stat", Length = 1, SqlParameterDbType = typeof(EnumToStringConvert), IsNullable = true)]
        [Display(Name = "Status")]
        public EnumStatus? Status { get; set; }

        [SugarColumn(ColumnName = "due_date", IsNullable = true)]
        [Display(Name = "Due Date")]
        [DataType(DataType.Date)]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd}", ApplyFormatInEditMode = true)]
        public DateTime? DueDate { get; set; }

        [SugarColumn(ColumnName = "unit_cost_shipped", ColumnDataType = "UnitcostType", IsNullable = true)]
        [Display(Name = "Unit Cost Shipped")]
        [Range(0, double.MaxValue, ErrorMessage = "Unit cost shipped must be greater than or equal to 0")]
        public decimal UnitCostShipped { get; set; } = 0;

        [SugarColumn(ColumnName = "u_m", ColumnDataType = "UmType", IsNullable = true)]
        [StringLength(3, ErrorMessage = "Unit of measure cannot exceed 3 characters")]
        [Display(Name = "Unit of Measure")]
        public string? UnitOfMeasure { get; set; }

        [SugarColumn(ColumnName = "loc", ColumnDataType = "locType", IsNullable = true)]
        [StringLength(5, ErrorMessage = "Location cannot exceed 5 characters")]
        [Display(Name = "Location")]
        public string? Location { get; set; }

        [SugarColumn(ColumnName = "CreatedDate", IsNullable = true)]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        [SugarColumn(ColumnName = "RecordDate", IsNullable = true)]
        public DateTime RecordDate { get; set; } = DateTime.Now;

        [SugarColumn(ColumnName = "CreatedBy", Length = 40, IsNullable = true)]
        [StringLength(40, ErrorMessage = "Created by cannot exceed 40 characters")]
        public string CreatedBy { get; set; } = string.Empty;

        [SugarColumn(ColumnName = "ModifiedBy", Length = 40, IsNullable = true)]
        [StringLength(40, ErrorMessage = "Modified by cannot exceed 40 characters")]
        public string ModifiedBy { get; set; } = string.Empty;

        // Navigation property for item description (not mapped to database)
        [SugarColumn(IsIgnore = true)]
        [Display(Name = "Item Description")]
        public string? ItemDesc { get; set; }
    }
    
    public class OrderShippingDto {
        [Required(ErrorMessage = "CO Number is required")]
        [Display(Name = "CO Number")]
        public string CoNum { get; set; } = string.Empty;

        [Required(ErrorMessage = "Line item number is required")]
        [Display(Name = "CO Line")]
        public int CoLine { get; set; }

        [Required(ErrorMessage = "Item number is required")]
        [Display(Name = "Item")]
        public string Item { get; set; } = string.Empty;

        [Display(Name = "Location")]
        [Required(ErrorMessage = "Location is required")]
        [StringLength(10, ErrorMessage = "Location code must not exceed {1} characters")]
        public string Location { get; set; } = string.Empty;

        [Display(Name = "Unit of Measure")]
        [StringLength(3, ErrorMessage = "UOM must not exceed {1} characters")]
        public string? UnitOfMeasure { get; set; }

        [Display(Name = "Ordered Qty")] public decimal QtyOrdered { get; set; }

        [Display(Name = "Shipped Qty")] public decimal QtyShipped { get; set; }

        [Display(Name = "Shipping Qty")] public decimal QtyShipping { get; set; }

        [Display(Name = "Reason Code")] public string? ReasonCode { get; set; }

        [Display(Name = "Info")] public string? Info { get; set; }

        [Display(Name = "Transaction Date")] public DateTime? TransDate { get; set; }
    }

    public class CoInfoDto
    {
        public string? CoNum { get; set; }
        public string? CustNum { get; set; }
        public DateTime? CoDate { get; set; }
        public EnumStatus? Status { get; set; }
    }

    [SugarTable("famcode")]
    public class FamilyCode : IEntity<string> {
        [SugarColumn(ColumnName = "family_code", IsPrimaryKey = true, Length = 15)]
        [Required(ErrorMessage = "Family code is required")]
        [StringLength(15, ErrorMessage = "Family code cannot exceed 15 characters")]
        public string Id { get; set; } = string.Empty;

        [SugarColumn(ColumnName = "description", Length = 25)]
        [Required(ErrorMessage = "Description is required")]
        [StringLength(25, ErrorMessage = "Description cannot exceed 25 characters")]
        public string Description { get; set; } = string.Empty;

        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public string CreatedBy { get; set; } = string.Empty;
        public string ModifiedBy { get; set; } = string.Empty;
        public DateTime RecordDate { get; set; } = DateTime.Now;
    }
}