using System.Globalization;
using BootstrapBlazor.Components;
using Microsoft.AspNetCore.Components.Routing;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Components;
using HuaLingErpApp.Client.Services;
using System.Security.Claims;

namespace HuaLingErpApp.Client.Layout {
    /// <summary>
    /// Main layout component with authentication support
    /// </summary>
    public partial class MainLayout {
        private bool UseTabSet { get; set; } = true;

        private string Theme { get; set; } = "";

        private bool IsOpen { get; set; }

        private bool IsFixedHeader { get; set; } = true;

        private bool IsFixedFooter { get; set; } = true;

        private bool IsFullSide { get; set; } = true;

        private bool ShowFooter { get; set; } = true;

        private List<MenuItem>? Menus { get; set; }
        private string CurrentUserName { get; set; } = "Guest";
        private string CurrentDisplayName { get; set; } = "Guest User";
        private List<string> CurrentUserRoles { get; set; } = new();

        /// <summary>
        /// OnInitialized method
        /// </summary>
        protected override async Task OnInitializedAsync() {
            await base.OnInitializedAsync();

            // Force English culture at layout level
            var culture = new CultureInfo("en-US");
            CultureInfo.CurrentCulture = culture;
            CultureInfo.CurrentUICulture = culture;
            Thread.CurrentThread.CurrentCulture = culture;
            Thread.CurrentThread.CurrentUICulture = culture;

            // Subscribe to authentication state changes
            AuthStateProvider.AuthenticationStateChanged += OnAuthenticationStateChanged;

            // Get current authentication state
            await UpdateUserInfo();

            Menus = GetIconSideMenuItems();
        }

        private async void OnAuthenticationStateChanged(Task<AuthenticationState> task) {
            await UpdateUserInfo();

            // Regenerate menus based on new authentication state
            Menus = GetIconSideMenuItems();

            await InvokeAsync(StateHasChanged);
        }

        private async Task UpdateUserInfo() {
            var authState = await AuthStateProvider.GetAuthenticationStateAsync();
            var user = authState.User;

            if (user.Identity?.IsAuthenticated == true) {
                CurrentUserName = user.Identity.Name ?? "Unknown";
                CurrentDisplayName = user.FindFirst("DisplayName")?.Value ?? CurrentUserName;
                CurrentUserRoles = user.FindAll(ClaimTypes.Role).Select(c => c.Value).ToList();
            }
            else {
                CurrentUserName = "Guest";
                CurrentDisplayName = "Guest User";
                CurrentUserRoles = new List<string>();
            }
        }

        public void Dispose() {
            AuthStateProvider.AuthenticationStateChanged -= OnAuthenticationStateChanged;
        }

        private List<MenuItem> GetIconSideMenuItems() {
            var menus = new List<MenuItem> {
                new() {
                    Text = "Dashboard", Icon = "fa-solid fa-fw fa-tachometer-alt", Url = "/", Match = NavLinkMatch.All
                }
            };

            // Only show menus if user is authenticated
            if (CurrentUserRoles.Contains("Administrator") || CurrentUserRoles.Contains("Manager")) {
                // Procurement menu
                menus.Add(new() {
                    Text = "Procurement",
                    Icon = "fa-solid fa-fw fa-shopping-cart",
                    Items = new List<MenuItem> {
                        new() {
                            Text = "Purchase Orders",
                            Icon = "fa-solid fa-fw fa-file-invoice-dollar",
                            Url = "/procurement/purchaseorders",
                            Match = NavLinkMatch.All
                        },
                        new() {
                            Text = "Purchase Order Lines",
                            Icon = "fa-solid fa-fw fa-list",
                            Url = "/procurement/purchaseorderlines",
                            Match = NavLinkMatch.Prefix
                        },
                        new() {
                            Text = "PO Receiving",
                            Icon = "fa-solid fa-fw fa-truck-ramp-box",
                            Url = "/procurement/purchaseorderreceiving",
                            Match = NavLinkMatch.Prefix
                        },
                        new() {
                            Text = "Vendors",
                            Icon = "fa-solid fa-fw fa-building",
                            Url = "/procurement/vendors",
                            Match = NavLinkMatch.All
                        },
                    },
                    Match = NavLinkMatch.All
                });

                // Sales menu
                menus.Add(new() {
                    Text = "Sales",
                    Icon = "fa-solid fa-fw fa-handshake",
                    Items = new List<MenuItem> {
                        new() {
                            Text = "Customers",
                            Icon = "fa-solid fa-fw fa-users",
                            Url = "/customer/customers",
                            Match = NavLinkMatch.All
                        },
                        new() {
                            Text = "Customer Orders",
                            Icon = "fa-solid fa-fw fa-file-invoice",
                            Url = "/sales/customerorders",
                            Match = NavLinkMatch.All
                        },
                        new() {
                            Text = "Customer Order Lines",
                            Icon = "fa-solid fa-fw fa-list",
                            Url = "/sales/customerorderlines",
                            Match = NavLinkMatch.All
                        },
                        new() {
                            Text = "Order Shipping",
                            Icon = "fa-solid fa-fw fa-truck",
                            Url = "/sales/ordershipping",
                            Match = NavLinkMatch.All
                        }
                    },
                    Match = NavLinkMatch.All
                });

                // Item Management menu
                menus.Add(new() {
                    Text = "Item Management",
                    Icon = "fa-solid fa-fw fa-boxes-stacked",
                    Items = new List<MenuItem> {
                        new() {
                            Text = "Items",
                            Icon = "fa-solid fa-fw fa-box",
                            Url = "/item/items",
                            Match = NavLinkMatch.All
                        },
                        new() {
                            Text = "Family Codes",
                            Icon = "fa-solid fa-fw fa-tags",
                            Url = "/item/familycodes",
                            Match = NavLinkMatch.All
                        },
                        new() {
                            Text = "Units of Measure",
                            Icon = "fa-solid fa-fw fa-ruler",
                            Url = "/item/ums",
                            Match = NavLinkMatch.All
                        }
                    },
                    Match = NavLinkMatch.All
                });
                // Finance menu
                menus.Add(new() {
                    Text = "Finance",
                    Icon = "fa-solid fa-fw fa-calculator",
                    Items = new List<MenuItem> {
                        new() {
                            Text = "Currency Codes",
                            Icon = "fa-solid fa-fw fa-dollar-sign",
                            Url = "/finance/currencycodes",
                            Match = NavLinkMatch.All
                        },
                        new() {
                            Text = "Unit Code 1",
                            Icon = "fa-solid fa-fw fa-code",
                            Url = "/finance/unitcode1s",
                            Match = NavLinkMatch.All
                        },
                        new() {
                            Text = "Unit Code 2",
                            Icon = "fa-solid fa-fw fa-code",
                            Url = "/finance/unitcode2s",
                            Match = NavLinkMatch.All
                        },
                        new() {
                            Text = "Material Tran Amt",
                            Icon = "fa-solid fa-fw fa-receipt",
                            Url = "/finance/matltranamt",
                            Match = NavLinkMatch.All
                        },
                        new() {
                            Text = "Parameters",
                            Icon = "fa-solid fa-fw fa-sliders-h",
                            Url = "/finance/parameters",
                            Match = NavLinkMatch.All
                        }
                    },
                    Match = NavLinkMatch.All
                });
            }

            ;

            // Add admin menu for administrators
            if (CurrentUserRoles.Contains("Administrator")) {
                menus.Add(new MenuItem {
                    Text = "Administration",
                    Icon = "fa-solid fa-fw fa-cogs",
                    Items = new List<MenuItem> {
                        new() {
                            Text = "User Management",
                            Icon = "fa-solid fa-fw fa-users",
                            Url = "/admin/users",
                            Match = NavLinkMatch.All
                        }
                    },
                    Match = NavLinkMatch.All
                });
            }

            // Add system tools for administrators and managers
            if (CurrentUserRoles.Contains("Administrator") || CurrentUserRoles.Contains("Manager")) {
                menus.Add(new MenuItem {
                    Text = "System Tools",
                    Icon = "fa-solid fa-fw fa-tools",
                    Items = new List<MenuItem> {
                        new() {
                            Text = "Hangfire Dashboard",
                            Icon = "fa-solid fa-fw fa-tasks",
                            Url = "/hangfire",
                            Match = NavLinkMatch.All
                        }
                    },
                    Match = NavLinkMatch.All
                });
            }

            return menus;
        }

        private async Task HandleLogout() {
            // For server-side rendering, we'll use a simple approach
            Navigation.NavigateTo("/api/auth/logout", true);
        }
    }
}