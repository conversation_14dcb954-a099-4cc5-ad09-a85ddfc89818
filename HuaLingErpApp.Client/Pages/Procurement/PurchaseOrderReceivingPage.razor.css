.page-container {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 120px);
    min-height: 500px;
    gap: 0.25rem;
    padding: 1rem;
}

.toolbar-container {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 0.375rem;
    border: 1px solid #dee2e6;
    margin-bottom: 1.5rem;
}

.table-container {
    flex: 0 0 60%;
    min-height: 0;
    overflow-y: auto;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    margin-top: 0.5rem;
}

.inventory-container {
    flex: 1;
    min-height: 200px;
    overflow-y: auto;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 1rem;
    margin-top: 1rem;
}

.inventory-container h6 {
    margin: 0 0 0.75rem 0;
    font-weight: 600;
    font-size: 1rem;
    color: #495057;
}

.custom-table {
    width: 100%;
    margin-bottom: 0;
}

.custom-table th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    font-size: 0.875rem;
    padding: 0.75rem 0.5rem;
    text-align: center;
    vertical-align: middle;
    white-space: nowrap;
}

.custom-table td {
    padding: 0.5rem;
    text-align: center;
    vertical-align: middle;
    border-bottom: 1px solid #dee2e6;
    font-size: 0.875rem;
}

.custom-table tbody tr:hover {
    background-color: #f8f9fa;
}

.edited-row {
    background-color: #e7f3ff !important;
    border-left: 4px solid #007bff;
}

.edited-row:hover {
    background-color: #d1ecf1 !important;
}

.form-control-sm {
    font-size: 0.875rem;
    padding: 0.25rem 0.5rem;
}

.pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background-color: #f8f9fa;
    border-top: 1px solid #dee2e6;
}

.search-container {
    margin-bottom: 1rem;
}

.alert-custom {
    padding: 0.75rem 1rem;
    margin-bottom: 1rem;
    border: 1px solid transparent;
    border-radius: 0.375rem;
}

.alert-success {
    color: #155724;
    background-color: #d4edda;
    border-color: #c3e6cb;
}

.alert-danger {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
}

.alert-warning {
    color: #856404;
    background-color: #fff3cd;
    border-color: #ffeaa7;
}

.modal-backdrop-custom {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1040;
}

.modal-custom {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1050;
    background: white;
    border-radius: 0.375rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    max-width: 500px;
    width: 90%;
}

/* 工具栏组件高度统一 */
.toolbar-container .input-group-text {
    height: 38px;
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    line-height: 1.5;
}

.toolbar-container .btn {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    height: 38px;
    line-height: 1.5;
}

/* BootstrapBlazor Select 组件样式调整 */
.toolbar-container .form-select, 
.toolbar-container .select .form-select,
.toolbar-container .bb-select .form-select {
    height: 38px !important;
    padding: 0.5rem 0.75rem !important;
    font-size: 0.875rem !important;
}

/* BootstrapBlazor DateTimePicker 组件样式调整 */
.toolbar-container .datetime-picker .form-control,
.toolbar-container .bb-datetime .form-control {
    height: 38px !important;
    padding: 0.5rem 0.75rem !important;
    font-size: 0.875rem !important;
}

.input-group-sm .form-control {
    font-size: 0.875rem;
    padding: 0.5rem 0.75rem;
    height: 38px;
}

.input-group-sm .input-group-text {
    font-size: 0.875rem;
    padding: 0.5rem 0.75rem;
    height: 38px;
}

/* 原生下拉框样式 */
.form-select {
    display: block;
    width: 100%;
    padding: 0.375rem 2.25rem 0.375rem 0.75rem;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: #212529;
    background-color: #fff;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 0.75rem center;
    background-size: 16px 12px;
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    appearance: none;
}

.form-select:focus {
    border-color: #86b7fe;
    outline: 0;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.form-select:disabled {
    background-color: #e9ecef;
    opacity: 1;
}

.form-select-sm {
    padding-top: 0.25rem;
    padding-bottom: 0.25rem;
    padding-left: 0.5rem;
    font-size: 0.875rem;
    border-radius: 0.25rem;
}

/* 表格内的下拉框样式 */
.custom-table .form-select {
    font-size: 0.875rem;
    padding: 0.25rem 1.5rem 0.25rem 0.5rem;
    min-width: 120px;
    border: 1px solid #dee2e6;
}

.custom-table .form-select:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* 悬停效果 */
.custom-table .form-select:hover {
    border-color: #adb5bd;
}

/* 选项样式 */
.form-select option {
    padding: 0.5rem;
    color: #212529;
    background-color: #fff;
}

.form-select option:hover {
    background-color: #f8f9fa;
}

.form-select option:checked {
    background-color: #0d6efd;
    color: #fff;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .custom-table .form-select {
        font-size: 0.75rem;
        min-width: 100px;
        padding: 0.2rem 1.2rem 0.2rem 0.4rem;
    }
}
