@page "/procurement/purchaseorderreceiving"
@page "/procurement/purchaseorderreceiving/{PoNum}"

@using HuaLingErpApp.Shared
@using System.ComponentModel.DataAnnotations
@using HuaLingErpApp.Client.Components
@using SqlSugar
@using Console = System.Console
@inject IJSRuntime JSRuntime
@inject ApiService Api
@inject NavigationManager NavigationManager
@inject MessageService MessageService

<SecurePage RequiredRoles="Administrator,Manager" PageName="Purchase Order Receiving">
    <PageTitle>Purchase Order Receiving</PageTitle>
<!-- Alert Messages -->
@if (!string.IsNullOrEmpty(_alertMessage))
{
    <div class="alert-custom @_alertClass">
        @_alertMessage
        <button type="button" class="btn-close float-end" @onclick="ClearAlert"></button>
    </div>
}

<div class="page-container">
    @if (_isReady) {
        <!-- Toolbar -->
        <div class="toolbar-container">
            <div class="row g-3 align-items-center">
                <div class="col-md-4">
                    <div class="input-group">
                        <label class="input-group-text">PO Number</label>
                        <Select TValue="string" Items="PoNumSelectedItems"
                                OnSelectedItemChanged="OnSelectedPoNumChanged"
                                @bind-Value="Model.PoNum" ShowSearch="true"
                                IsPopover="true"
                                PlaceHolder="Select PoNum...">
                            <Options>
                                <SelectOption Value="" Text="Please select..." IsDisabled="true"
                                              Active="true"></SelectOption>
                            </Options>
                        </Select>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="input-group">
                        <label class="input-group-text">Date</label>
                        <DateTimePicker @bind-Value="@_selectedDateTime" ShowIcon="false" IsEditable="true" DateFormat="yyyy-MM-dd"/>
                    </div>
                </div>
                <div class="col-md-5">
                    <div class="d-flex justify-content-end align-items-center gap-2">
                        <div class="text-muted small me-3">
                            Selected: @_selectedRows.Count | Edited: @_editedRows.Count
                        </div>
                        <button class="btn btn-outline-primary" @onclick="RefreshTable">
                            <i class="fas fa-sync-alt me-1"></i>Refresh
                        </button>
                        <button class="btn btn-success" @onclick="OnReceiveButtonClick" disabled="@_isReceiveButtonDisabled">
                            <i class="fas fa-clipboard-check me-1"></i>Receive (@_selectedRows.Count)
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <!-- Table Container -->
        <div class="table-container">
            <table class="table table-striped table-bordered table-hover custom-table">
                <thead>
                    <tr>
                        <th style="width: 50px;">
                            <input type="checkbox" @onchange="OnSelectAllChanged" checked="@IsAllSelected" />
                        </th>
                        <th style="width: 100px;">PO Number</th>
                        <th style="width: 80px;">PO Line</th>
                        <th style="width: 120px;">Item</th>
                        <th style="width: 100px;">Location</th>
                        <th style="width: 60px;">U/M</th>
                        <th style="width: 100px;">Qty Ordered</th>
                        <th style="width: 100px;">Qty Received</th>
                        <th style="width: 120px;">Qty Receiving</th>
                        <th style="width: 120px;">Reason Code</th>
                        <th style="width: 150px;">Info</th>
                    </tr>
                </thead>
                <tbody>
                    @if (FilteredItems.Any())
                    {
                        @foreach (var item in PaginatedItems)
                        {
                            var rowKey = GetRowKey(item);
                            var isSelected = _selectedRows.Contains(rowKey);
                            var isEdited = _editedRows.Contains(rowKey);
                            var isEditing = _editingRows.ContainsKey(rowKey) && _editingRows[rowKey];
                            <tr class="@(isSelected ? "edited-row" : "")" @ondblclick="@(() => OnRowDoubleClick(item))" @onclick="@(() => OnRowClick(item))" style="cursor: pointer;">
                                <td @onclick:stopPropagation="true">
                                    <input type="checkbox" @onchange="@((e) => OnRowCheckboxChanged(item, e))" checked="@isSelected" />
                                </td>
                                <td>@item.PoNum</td>
                                <td>@item.PoLine</td>
                                <td>@item.Item</td>
                                <td>@item.Location</td>
                                <td>@item.UnitOfMeasure</td>
                                <td>@item.QtyOrdered.ToString("F2")</td>
                                <td>@item.QtyReceived.ToString("F2")</td>
                                <td @onclick:stopPropagation="true">
                                    @if (isEditing)
                                    {
                                        <input type="number" class="form-control form-control-sm"
                                               @bind="item.QtyReceiving"
                                               @oninput="@(async (e) => await OnQtyReceivingInput(item, e))"
                                               @onblur="@(async () => await OnQtyReceivingChanged(item))"
                                               step="0.01"
                                               style="width: 100px;" />
                                    }
                                    else
                                    {
                                        <span>@item.QtyReceiving.ToString("F2")</span>
                                    }
                                </td>
                                <td @onclick:stopPropagation="true">
                                    @if (isEditing && item.QtyReceiving < 0)
                                    {
                                        <Select TValue="string" @bind-Value="@item.ReasonCode"
                                                Items="ReasonCodeSelectedItems"
                                                OnSelectedItemChanged="@(async (selectedItem) => await OnReasonCodeChanged(item, selectedItem))"
                                                ShowSearch="true"
                                                IsPopover="true"
                                                PlaceHolder="Select ReasonCode..."
                                                IsClearable="true">
                                            <Options>
                                                <SelectOption Value="" Text="Please select..." IsDisabled="true"
                                                              Active="true"></SelectOption>
                                            </Options>
                                        </Select>
                                    }
                                    else if (item.QtyReceiving < 0)
                                    {
                                        <span>@item.ReasonCode</span>
                                    }
                                    else
                                    {
                                        <span class="text-muted">N/A</span>
                                    }
                                </td>
                                <td>@item.Info</td>
                            </tr>
                        }
                    }
                    else
                    {
                        <tr>
                            <td colspan="11" class="text-center text-muted py-4">
                                @if (string.IsNullOrEmpty(Model.PoNum))
                                {
                                    <span>Please select a PO Number to view items</span>
                                }
                                else
                                {
                                    <span>No items found for PO @Model.PoNum</span>
                                }
                            </td>
                        </tr>
                    }
                </tbody>
            </table>

            <!-- Pagination -->
            @if (FilteredItems.Any())
            {
                <div class="pagination-container">
                    <div class="d-flex justify-content-between align-items-center w-100">
                        <div class="d-flex align-items-center">
                            <span class="me-3 small text-muted">
                                Showing @Math.Min((_currentPage - 1) * _pageSize + 1, TotalItems) to @Math.Min(_currentPage * _pageSize, TotalItems) of @TotalItems items
                            </span>
                            <div class="input-group input-group-sm" style="width: 120px;">
                                <span class="input-group-text">Size</span>
                                <select class="form-select" @bind="_pageSize" @bind:event="onchange" @bind:after="OnPageSizeChanged">
                                    <option value="5">5</option>
                                    <option value="10">10</option>
                                    <option value="15">15</option>
                                    <option value="20">20</option>
                                    <option value="25">25</option>
                                    <option value="30">30</option>
                                </select>
                            </div>
                        </div>
                        <nav>
                            <ul class="pagination pagination-sm mb-0">
                                <li class="page-item @(_currentPage == 1 ? "disabled" : "")">
                                    <button class="page-link" @onclick="() => GoToPage(1)" disabled="@(_currentPage == 1)">First</button>
                                </li>
                                <li class="page-item @(_currentPage == 1 ? "disabled" : "")">
                                    <button class="page-link" @onclick="() => GoToPage(_currentPage - 1)" disabled="@(_currentPage == 1)">Previous</button>
                                </li>

                                @for (int i = Math.Max(1, _currentPage - 2); i <= Math.Min(TotalPages, _currentPage + 2); i++)
                                {
                                    var pageNum = i;
                                    <li class="page-item @(_currentPage == pageNum ? "active" : "")">
                                        <button class="page-link" @onclick="() => GoToPage(pageNum)">@pageNum</button>
                                    </li>
                                }

                                <li class="page-item @(_currentPage == TotalPages ? "disabled" : "")">
                                    <button class="page-link" @onclick="() => GoToPage(_currentPage + 1)" disabled="@(_currentPage == TotalPages)">Next</button>
                                </li>
                                <li class="page-item @(_currentPage == TotalPages ? "disabled" : "")">
                                    <button class="page-link" @onclick="() => GoToPage(TotalPages)" disabled="@(_currentPage == TotalPages)">Last</button>
                                </li>
                            </ul>
                        </nav>
                    </div>
                </div>
            }
        </div>

        <!-- Inventory Information -->
        @if (_showInventoryTable && ItemInventories != null) {
            <div class="inventory-container">
                <h6>Inventory Information</h6>
                @if (ItemInventories.Any()) {
                    <table class="table table-striped table-bordered table-sm">
                        <thead>
                            <tr>
                                <th>Item</th>
                                <th>Description</th>
                                <th>Location</th>
                                <th>Qty On Hand</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var inventory in ItemInventories)
                            {
                                <tr>
                                    <td>@inventory.Item?.Id</td>
                                    <td>@inventory.Item?.Description</td>
                                    <td>@inventory.Location</td>
                                    <td>@inventory.QuantityOnHand.ToString("F2")</td>
                                </tr>
                            }
                        </tbody>
                    </table>
                }
                else {
                    <div class="alert alert-warning py-2 my-2">No inventory information found</div>
                }
            </div>
        }
    }
</div>

<!-- Result Modal -->
@if (_showResultModal)
{
    <div class="modal-backdrop-custom" @onclick="CloseResultModal"></div>
    <div class="modal-custom">
        <div class="modal-header p-3 border-bottom">
            <h5 class="modal-title">Processing Results</h5>
        </div>
        <div class="modal-body p-3">
            <div class="row text-center">
                <div class="col-6">
                    <div class="card border-success">
                        <div class="card-body py-2">
                            <h3 class="text-success mb-1">@_successCount</h3>
                            <small class="text-muted">Successful</small>
                        </div>
                    </div>
                </div>
                <div class="col-6">
                    <div class="card border-danger">
                        <div class="card-body py-2">
                            <h3 class="text-danger mb-1">@_failureCount</h3>
                            <small class="text-muted">Failed</small>
                        </div>
                    </div>
                </div>
            </div>
            @if (!string.IsNullOrEmpty(_resultMessage))
            {
                <div class="mt-3">
                    <div class="alert alert-info py-2 mb-0">
                        @_resultMessage
                    </div>
                </div>
            }
        </div>
        <div class="modal-footer p-3 border-top">
            <button type="button" class="btn btn-primary btn-sm" @onclick="CloseResultModal">OK</button>
        </div>
    </div>
}
</SecurePage>
@code {
    [Parameter] public string? PoNum { get; set; }

    // Core data
    private bool _isReady;
    private bool _isReceiveButtonDisabled = false;
    public PurchaseOrderReceivingDto Model { get; set; } = new();
    private List<PurchaseOrderReceivingDto> PoReceivingItems { get; set; } = [];
    private List<PoItem> PoLineItems { get; set; } = [];
    private List<string> PoNumItems { get; set; } = [];
    private List<ReasonCode> ReasonCodes { get; set; } = [];
    private List<ItemInventoryDto>? ItemInventories = [];
    private bool _showInventoryTable;
    private DateTime _selectedDateTime = DateTime.Now;

    // Row selection management
    private HashSet<string> _editedRows = new();
    private HashSet<string> _selectedRows = new();
    private Dictionary<string, bool> _editingRows = new();

    // Pagination
    private int _currentPage = 1;
    private int _pageSize = 10;

    // Alert system
    private string _alertMessage = "";
    private string _alertClass = "";

    // Result modal
    private bool _showResultModal = false;
    private int _successCount = 0;
    private int _failureCount = 0;
    private string _resultMessage = "";

    // Computed properties
    private IEnumerable<PurchaseOrderReceivingDto> FilteredItems
    {
        get
        {
            return PoReceivingItems.AsEnumerable();
        }
    }

    private bool IsAllSelected => PoReceivingItems.Any() && PoReceivingItems.All(item => _selectedRows.Contains(GetRowKey(item)));

    private IEnumerable<SelectedItem> ReasonCodeSelectedItems => ReasonCodes
        .Select(c => new SelectedItem(c.ReasonCodeValue, $"{c.ReasonCodeValue}"));

    private IEnumerable<SelectedItem> PoNumSelectedItems => PoNumItems
        .Select(p => new SelectedItem(p, $"{p}"));

    private IEnumerable<PurchaseOrderReceivingDto> PaginatedItems
    {
        get
        {
            return FilteredItems
                .Skip((_currentPage - 1) * _pageSize)
                .Take(_pageSize);
        }
    }

    private int TotalItems => FilteredItems.Count();
    private int TotalPages => (int)Math.Ceiling((double)TotalItems / _pageSize);

    protected override async Task OnInitializedAsync() {
        var authState = await AuthStateProvider.GetAuthenticationStateAsync();
        if (authState.User.Identity?.IsAuthenticated == true) {
            await LoadPoNumsAsync();
            await LoadReasonCodesAsync();
            await LoadPoReceivingItemsAsync();
            _isReady = true;
        }
    }

    protected override async Task OnParametersSetAsync()
    {
        if (!string.IsNullOrEmpty(PoNum) && PoNum != Model.PoNum)
        {
            Model.PoNum = PoNum;
            await OnSelectedPoNumChanged(new SelectedItem(PoNum, PoNum));
        }
    }

    private string GetRowKey(PurchaseOrderReceivingDto item) => $"{item.PoNum}-{item.PoLine}";

    // Alert methods
    private void ShowAlert(string message, string type = "success")
    {
        _alertMessage = message;
        _alertClass = $"alert-{type}";
        StateHasChanged();
    }

    private void ClearAlert()
    {
        _alertMessage = "";
        _alertClass = "";
        StateHasChanged();
    }

    // Selection methods
    private void OnSelectAllChanged(ChangeEventArgs e)
    {
        var isChecked = (bool)(e.Value ?? false);

        if (isChecked)
        {
            foreach (var item in PoReceivingItems)
            {
                _selectedRows.Add(GetRowKey(item));
            }
        }
        else
        {
            _selectedRows.Clear();
        }

        StateHasChanged();
    }

    private void OnRowCheckboxChanged(PurchaseOrderReceivingDto item, ChangeEventArgs e)
    {
        var isChecked = (bool)(e.Value ?? false);
        var rowKey = GetRowKey(item);

        if (isChecked)
        {
            _selectedRows.Add(rowKey);
        }
        else
        {
            _selectedRows.Remove(rowKey);
        }

        StateHasChanged();
    }

    private async Task RefreshTable()
    {
        // Clear inventory information when refreshing
        ItemInventories?.Clear();
        _showInventoryTable = false;

        if (!string.IsNullOrEmpty(Model.PoNum))
        {
            await LoadPoLinesAsync(Model.PoNum);
            if (PoLineItems.Count > 0)
            {
                await LoadPoReceivingItemsAsync();
                _isReceiveButtonDisabled = false;
            }
        }

        ShowAlert("Table refreshed successfully", "success");
    }

    // Pagination methods
    private void GoToPage(int page)
    {
        if (page >= 1 && page <= TotalPages)
        {
            _currentPage = page;
            StateHasChanged();
        }
    }

    private void OnPageSizeChanged()
    {
        _currentPage = 1; // Reset to first page
        StateHasChanged();
    }

    // Result modal methods
    private void ShowResultModal(int successCount, int failureCount, string message = "")
    {
        _successCount = successCount;
        _failureCount = failureCount;
        _resultMessage = message;
        _showResultModal = true;
        StateHasChanged();
    }

    private void CloseResultModal()
    {
        _showResultModal = false;
        StateHasChanged();
    }

    private async Task LoadPoReceivingItemsAsync() {
        try {
            PoReceivingItems.Clear();
            _editedRows.Clear(); // Clear edited rows when loading new data
            _selectedRows.Clear(); // Clear selected rows when loading new data
            _editingRows.Clear(); // Clear editing state when loading new data

            if (PoLineItems.Count > 0) {
                var dtos = PoLineItems.Select(item => new PurchaseOrderReceivingDto {
                    PoNum = item.PoNum ?? string.Empty,
                    PoLine = item.PoLine,
                    Item = item.Item,
                    UnitOfMeasure = item.UnitOfMeasure,
                    Location = item.Location,
                    QtyOrdered = item.QtyOrdered,
                    QtyReceived = item.QtyReceived,
                    QtyReceiving = 0,
                    ReasonCode = null,
                    Info = null
                }).ToList();

                PoReceivingItems.AddRange(dtos);
            }

            StateHasChanged();
        }
        catch (Exception ex) {
            ShowAlert($"Failed to load data: {ex.Message}", "danger");
        }
    }

    private async Task LoadPoNumsAsync() {
        try {
            var response = await Api.GetAsync<HttpResponseModel<List<string>>>("api/purchaseorder/ponums");
            if (response?.IsSuccess == true && response.Data != null) {
                PoNumItems = response.Data;
            }
            else {
                ShowAlert($"Failed to load PO Numbers: {response?.Message ?? "Unknown error"}", "danger");
            }
        }
        catch (Exception ex) {
            ShowAlert($"Failed to load PO Numbers: {ex.Message}", "danger");
        }
    }

    private async Task LoadPoLinesAsync(string poNum) {
        try {
            var response = await Api.GetAsync<HttpResponseModel<List<PoItem>>>($"api/purchaseorderlines/byponum/{poNum}");
            if (response?.IsSuccess == true) {
                PoLineItems = response.Data ?? new List<PoItem>();
                if (!PoLineItems.Any()) {
                    ShowAlert("No PO lines found for the selected PO number", "warning");
                }
            }
            else {
                PoLineItems = new List<PoItem>();
                ShowAlert(response?.Message ?? "Failed to load PO lines", "danger");
            }
        }
        catch (Exception ex) {
            PoLineItems = new List<PoItem>();
            ShowAlert("No PO lines found for the selected PO number", "danger");
        }
    }

    private async Task LoadReasonCodesAsync() {
        try {
            var response = await Api.GetAsync<HttpResponseModel<List<ReasonCode>>>("api/reasoncode");
            if (response?.IsSuccess == true) {
                ReasonCodes = response.Data ?? [];
            }
            else {
                ShowAlert(response?.Message ?? "Failed to load reason codes", "danger");
            }
        }
        catch (Exception ex) {
            ShowAlert($"Failed to load reason codes: {ex.Message}", "danger");
        }
    }

    private async Task OnSelectedPoNumChanged(SelectedItem selectedItem)
    {
        // Re-enable receive button
        _isReceiveButtonDisabled = false;

        // Clear current items and all states
        PoReceivingItems.Clear();
        _editedRows.Clear();
        _selectedRows.Clear();
        _editingRows.Clear();
        _currentPage = 1; // Reset pagination
        ClearAlert(); // Clear any existing alerts

        // Clear inventory information
        ItemInventories?.Clear();
        _showInventoryTable = false;

        StateHasChanged();

        // Load corresponding lines if PO number is selected
        if (!string.IsNullOrEmpty(Model.PoNum))
        {
            await LoadPoLinesAsync(Model.PoNum);
            if (PoLineItems.Count > 0)
            {
                await LoadPoReceivingItemsAsync();
            }
        }
    }

    private async Task OnRowClick(PurchaseOrderReceivingDto item)
    {
        if (string.IsNullOrEmpty(item.Item))
        {
            ShowAlert("Please select a valid item", "warning");
            return;
        }

        try
        {
            var response = await Api.GetAsync<HttpResponseModel<List<ItemInventoryDto>>>($"api/items/{item.Item}/inventory");
            if (response?.IsSuccess == true && response.Data != null)
            {
                ItemInventories = response.Data;
            }
            else
            {
                ShowAlert(response?.Message ?? "No inventory information found for the selected item", "warning");
            }
        }
        catch (Exception ex)
        {
            ShowAlert($"Failed to load inventory information: {ex.Message}", "danger");
        }
        finally
        {
            _showInventoryTable = true;
            StateHasChanged();
        }
    }

    private void OnRowDoubleClick(PurchaseOrderReceivingDto item)
    {
        var rowKey = GetRowKey(item);

        // Start editing this row
        _editingRows[rowKey] = true;

        // Also select this row
        _selectedRows.Add(rowKey);

        StateHasChanged();
    }



    private async Task OnReceiveButtonClick()
    {
        if (!_selectedRows.Any())
        {
            ShowAlert("Please select items to receive", "warning");
            return;
        }

        // Get selected items
        var selectedItems = PoReceivingItems
            .Where(item => _selectedRows.Contains(GetRowKey(item)))
            .ToList();

        // Validate selected items
        var invalidItems = selectedItems.Where(x => x.QtyReceiving == 0).ToList();
        if (invalidItems.Any())
        {
            ShowAlert("Please enter receiving quantity for all selected items", "warning");
            return;
        }

        var negativeItemsWithoutReason = selectedItems.Where(x => x.QtyReceiving < 0 && string.IsNullOrEmpty(x.ReasonCode)).ToList();
        if (negativeItemsWithoutReason.Any())
        {
            ShowAlert("Reason code is required for negative receiving quantities", "warning");
            return;
        }

        await ReceiveAndFillInfo(selectedItems);
    }

    private async Task ReceiveAndFillInfo(List<PurchaseOrderReceivingDto> list)
    {
        try
        {
            // Add transaction date to each item
            foreach (var item in list)
            {
                item.TransDate = _selectedDateTime;
            }

            // Call stored procedure API
            var response = await Api.PostAsync("api/purchaseorderreceiving/process-sp", list);

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var result = System.Text.Json.JsonSerializer.Deserialize<HttpResponseModel<List<PurchaseOrderReceivingDto>>>(content);

                if (result?.IsSuccess == true && result.Data != null)
                {
                    int successCount = 0;
                    int failureCount = 0;

                    // Update table data and count results
                    foreach (var updatedItem in result.Data)
                    {
                        var originalItem = PoReceivingItems.FirstOrDefault(x => x.PoNum == updatedItem.PoNum && x.PoLine == updatedItem.PoLine);
                        if (originalItem != null)
                        {
                            originalItem.Info = updatedItem.Info;

                            // Count success/failure based on Info content
                            if (!string.IsNullOrEmpty(updatedItem.Info) && !updatedItem.Info.Contains("Error") && !updatedItem.Info.Contains("Failed"))
                            {
                                successCount++;
                            }
                            else
                            {
                                failureCount++;
                            }
                        }
                    }

                    // Clear selected rows and editing state after successful processing
                    _selectedRows.Clear();
                    _editingRows.Clear();
                    _isReceiveButtonDisabled = true;

                    StateHasChanged();

                    // Show detailed result modal
                    ShowResultModal(successCount, failureCount, $"Processing completed for {list.Count} items");
                }
                else
                {
                    ShowAlert(result?.Message ?? "Failed to process items", "danger");
                }
            }
            else
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                var errorResponse = System.Text.Json.JsonSerializer.Deserialize<HttpResponseModel<object>>(errorContent);

                ShowAlert(errorResponse?.Message ?? "Failed to execute stored procedure", "danger");
            }
        }
        catch (Exception ex)
        {
            ShowAlert($"Error processing receiving: {ex.Message}", "danger");
        }
    }

    private async Task OnQtyReceivingInput(PurchaseOrderReceivingDto item, ChangeEventArgs e)
    {
        if (decimal.TryParse(e.Value?.ToString(), out var qty))
        {
            item.QtyReceiving = qty;

            // Clear reason code if quantity becomes non-negative
            if (qty >= 0)
            {
                item.ReasonCode = null;
            }

            // Mark row as edited
            var rowKey = GetRowKey(item);
            if (qty != 0) // Only mark as edited if quantity is not zero
            {
                _editedRows.Add(rowKey);
            }
            else
            {
                _editedRows.Remove(rowKey); // Remove from edited if quantity is reset to zero
            }

            // Update original item in the list
            var originalItem = PoReceivingItems.FirstOrDefault(x =>
                x.PoNum == item.PoNum && x.PoLine == item.PoLine);
            if (originalItem != null)
            {
                originalItem.QtyReceiving = qty;
                if (qty >= 0)
                {
                    originalItem.ReasonCode = null;
                }
            }

            StateHasChanged();
        }

        await Task.CompletedTask;
    }

    private async Task OnQtyReceivingChanged(PurchaseOrderReceivingDto item)
    {
        var rowKey = GetRowKey(item);
        // Stop editing when user finishes editing (blur event)
        _editingRows[rowKey] = false;

        Console.WriteLine($"QtyReceiving changed: {item.PoNum}-{item.PoLine} = {item.QtyReceiving}");
        StateHasChanged();
        await Task.CompletedTask;
    }

    private async Task OnReasonCodeChanged(PurchaseOrderReceivingDto item, SelectedItem selectedItem)
    {
        item.ReasonCode = selectedItem.Value;

        // Mark row as edited if reason code is selected
        var rowKey = GetRowKey(item);
        if (!string.IsNullOrEmpty(selectedItem.Value))
        {
            _editedRows.Add(rowKey);
        }

        // Update original item in the list
        var originalItem = PoReceivingItems.FirstOrDefault(x =>
            x.PoNum == item.PoNum && x.PoLine == item.PoLine);
        if (originalItem != null)
        {
            originalItem.ReasonCode = selectedItem.Value;
        }

        Console.WriteLine($"ReasonCode changed: {item.PoNum}-{item.PoLine} = {item.ReasonCode}");
        StateHasChanged();
        await Task.CompletedTask;
    }
}

