@page "/finance/matltran"
@using Console = System.Console

<SecurePage RequiredRoles="Administrator,Manager,User" PageName="Material Transactions">
@if (isReady) {
    <!-- Date Range Filter -->
    <div class="card mb-3">
        <div class="card-header">
            <h6 class="mb-0">
                <i class="fas fa-calendar-alt me-2"></i>Date Range Filter
            </h6>
        </div>
        <div class="card-body">
            <div class="row g-3 align-items-end">
                <div class="col-md-6">
                    <label class="form-label">Select Date Range:</label>
                    <DateTimeRange @bind-Value="@DateRangeValue" 
                                   OnValueChanged="OnDateRangeChanged"
                                   ShowSidebar="true" 
                                   ShowToday="true"
                                   AutoClose="true"
                                   DateFormat="yyyy-MM-dd"
                                   IsEditable="true" />
                </div>
                <div class="col-md-3">
                    <Button Color="Color.Primary" Icon="fa-solid fa-search" 
                            Text="Filter" OnClick="@FilterByDateRange"></Button>
                </div>
                <div class="col-md-3">
                    <Button Color="Color.Secondary" Icon="fa-solid fa-times" 
                            Text="Clear Filter" OnClick="@ClearDateFilter"></Button>
                </div>
            </div>
            @if (DateRangeValue.Start != DateTime.MinValue || DateRangeValue.End != DateTime.MinValue)
            {
                <div class="mt-2">
                    <small class="text-muted">
                        <i class="fas fa-info-circle me-1"></i>
                        Current filter: 
                        @if (DateRangeValue.Start != DateTime.MinValue)
                        {
                            @DateRangeValue.Start.ToString("yyyy-MM-dd")
                        }
                        @if (DateRangeValue.Start != DateTime.MinValue && DateRangeValue.End != DateTime.MinValue)
                        {
                            <span> to </span>
                        }
                        @if (DateRangeValue.End != DateTime.MinValue)
                        {
                            @DateRangeValue.End.ToString("yyyy-MM-dd")
                        }
                    </small>
                </div>
            }
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="row mb-3">
        <div class="col-md-2">
            <div class="card border-primary">
                <div class="card-body text-center">
                    <h6 class="card-title text-primary">@(Summary?.TotalRecords?.ToString() ?? "0")</h6>
                    <p class="card-text small">Total Records</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card border-success">
                <div class="card-body text-center">
                    <h6 class="card-title text-success">@(GetFormattedNumber(Summary?.TotalQuantity, "N2"))</h6>
                    <p class="card-text small">Total Quantity</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card border-warning">
                <div class="card-body text-center">
                    <h6 class="card-title text-warning">@(GetFormattedCurrency(Summary?.TotalCost))</h6>
                    <p class="card-text small">Total Cost</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card border-info">
                <div class="card-body text-center">
                    <h6 class="card-title text-info">@(Summary?.UniqueItems?.ToString() ?? "0")</h6>
                    <p class="card-text small">Unique Items</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card border-secondary">
                <div class="card-body text-center">
                    <h6 class="card-title text-secondary">@(Summary?.UniqueLocations?.ToString() ?? "0")</h6>
                    <p class="card-text small">Unique Locations</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card border-dark">
                <div class="card-body text-center">
                    <h6 class="card-title text-dark">@FilteredItemsCount</h6>
                    <p class="card-text small">Filtered Records</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Data Table -->
    <Table TItem="MatlTran"
           IsPagination="true" PageItemsSource="[20, 25, 30, 50]"
           IsStriped="true" IsBordered="true" TableSize="TableSize.Compact"
           ShowToolbar="true" ShowSearch="true" ShowExtendButtons="false"
           SearchModel="@SearchModel" SearchMode="SearchMode.Popup" ShowEmpty="true"
           OnQueryAsync="@OnQueryAsync" OnResetSearchAsync="@OnResetSearchAsync"
           ShowSkeleton="true"
           ShowAddButton="false" ShowEditButton="false" ShowDeleteButton="false"
           IsMultipleSelect="false">
        <TableColumns>
            <TableColumn @bind-Field="@context.TransNum" Text="Trans Number" Sortable="true" Searchable="true"
                         Align="Alignment.Center" Width="120"/>
            <TableColumn @bind-Field="@context.Item" Text="Item" Sortable="true" Searchable="true"
                         Align="Alignment.Center" Width="150"/>
            <TableColumn @bind-Field="@context.Qty" Text="Quantity" Sortable="true" 
                         Align="Alignment.Right" Width="100" FormatString="N2"/>
            <TableColumn @bind-Field="@context.TransDate" Text="Trans Date" Sortable="true" Searchable="true"
                         Align="Alignment.Center" Width="120" FormatString="yyyy-MM-dd"/>
            <TableColumn @bind-Field="@context.RefNum" Text="Ref Number" Sortable="true" Searchable="true"
                         Align="Alignment.Center" Width="100"/>
            <TableColumn @bind-Field="@context.RefLine" Text="Ref Line" Sortable="true" 
                         Align="Alignment.Center" Width="80"/>
            <TableColumn @bind-Field="@context.TransType" Text="Trans Type" Sortable="true" Searchable="true"
                         Align="Alignment.Center" Width="100"/>
            <TableColumn @bind-Field="@context.RefType" Text="Ref Type" Sortable="true" Searchable="true"
                         Align="Alignment.Center" Width="100"/>
            <TableColumn @bind-Field="@context.Cost" Text="Cost" Sortable="true" 
                         Align="Alignment.Right" Width="100" FormatString="C"/>
            <TableColumn @bind-Field="@context.Loc" Text="Location" Sortable="true" Searchable="true"
                         Align="Alignment.Center" Width="100"/>
            <TableColumn @bind-Field="@context.Lot" Text="Lot" Sortable="true" Searchable="true"
                         Align="Alignment.Center" Width="120"/>
            <TableColumn @bind-Field="@context.ReasonCode" Text="Reason Code" Sortable="true" Searchable="true"
                         Align="Alignment.Center" Width="120"/>
            <TableColumn @bind-Field="@context.UM" Text="U/M" Sortable="true" Searchable="true"
                         Align="Alignment.Center" Width="80"/>
        </TableColumns>
        <TableToolbarTemplate>
            <TableToolbarButton TItem="MatlTran" Color="Color.Primary" Icon="fa-solid fa-sync-alt"
                                Text="Refresh" OnClickCallback="RefreshData" />
            <TableToolbarButton TItem="MatlTran" Color="Color.Info" Icon="fa-solid fa-chart-bar"
                                Text="Summary" OnClickCallback="LoadSummary" />
        </TableToolbarTemplate>
    </Table>
}
</SecurePage>

@code {
    private List<MatlTran> Items { get; set; } = new();
    private List<MatlTran> FilteredItems { get; set; } = new();
    private MatlTran SearchModel { get; set; } = new();
    private dynamic? Summary { get; set; }
    private DateTimeRangeValue DateRangeValue { get; set; } = new();

    private string relativePath = "api/matltran";
    private bool isReady = false;
    private bool isDateFiltered = false;

    private int FilteredItemsCount => FilteredItems.Count;

    protected override async Task OnInitializedAsync() {
        // Check authentication state, only load data if authenticated
        var authState = await AuthStateProvider.GetAuthenticationStateAsync();
        if (authState.User.Identity?.IsAuthenticated == true) {
            // Initialize date range to last 30 days
            DateRangeValue = new DateTimeRangeValue
            {
                Start = DateTime.Today.AddDays(-30),
                End = DateTime.Today
            };
            
            await LoadDataSafely();
            await LoadSummary();
        }
    }

    private async Task LoadDataSafely() {
        try {
            await LoadData();
        }
        catch (Exception ex) {
            // Only show error message if authenticated
            var authState = await AuthStateProvider.GetAuthenticationStateAsync();
            if (authState.User.Identity?.IsAuthenticated == true) {
                await MessageService.Show(new MessageOption {
                    Content = $"Initialization failed: {ex.Message}",
                    Color = Color.Danger
                });
            }
        }
        finally {
            isReady = true;
            StateHasChanged();
        }
    }

    private async Task LoadData() {
        string endpoint = relativePath;
        
        // If date range is set, use the filtered endpoint
        if (isDateFiltered && (DateRangeValue.Start != DateTime.MinValue || DateRangeValue.End != DateTime.MinValue))
        {
            var queryParams = new List<string>();
            if (DateRangeValue.Start != DateTime.MinValue)
            {
                queryParams.Add($"startDate={DateRangeValue.Start:yyyy-MM-dd}");
            }
            if (DateRangeValue.End != DateTime.MinValue)
            {
                queryParams.Add($"endDate={DateRangeValue.End:yyyy-MM-dd}");
            }
            
            if (queryParams.Any())
            {
                endpoint = $"{relativePath}/bydaterange?{string.Join("&", queryParams)}";
            }
        }

        var response = await Api.GetAsync<HttpResponseModel<List<MatlTran>>>(endpoint);
        if (response?.IsSuccess == true && response.Data != null) {
            Items = response.Data;
            FilteredItems = Items; // Initially, filtered items are the same as all items
        }
        else {
            await MessageService.Show(new MessageOption {
                Content = response?.Message ?? "Failed to load material transactions",
                Color = Color.Danger
            });
        }
    }

    private async Task LoadSummary() {
        try {
            string endpoint = $"{relativePath}/summary";

            // Add date range parameters if filtering is active
            if (isDateFiltered && (DateRangeValue.Start != DateTime.MinValue || DateRangeValue.End != DateTime.MinValue))
            {
                var queryParams = new List<string>();
                if (DateRangeValue.Start != DateTime.MinValue)
                {
                    queryParams.Add($"startDate={DateRangeValue.Start:yyyy-MM-dd}");
                }
                if (DateRangeValue.End != DateTime.MinValue)
                {
                    queryParams.Add($"endDate={DateRangeValue.End:yyyy-MM-dd}");
                }

                if (queryParams.Any())
                {
                    endpoint = $"{endpoint}?{string.Join("&", queryParams)}";
                }
            }

            var response = await Api.GetAsync<HttpResponseModel<dynamic>>(endpoint);
            if (response?.IsSuccess == true && response.Data != null) {
                Summary = response.Data;
                StateHasChanged();
            }
        }
        catch (Exception ex) {
            // Silently fail for summary - it's not critical
            Console.WriteLine($"Failed to load summary: {ex.Message}");
        }
    }

    private async Task OnDateRangeChanged(DateTimeRangeValue value)
    {
        DateRangeValue = value;
        // Don't auto-filter on change, wait for user to click Filter button
        StateHasChanged();
    }

    private async Task FilterByDateRange()
    {
        isDateFiltered = true;
        await LoadData();
        await LoadSummary();

        await MessageService.Show(new MessageOption {
            Content = "Data filtered by date range successfully",
            Color = Color.Success
        });
    }

    private async Task ClearDateFilter()
    {
        isDateFiltered = false;
        DateRangeValue = new DateTimeRangeValue();
        await LoadData();
        await LoadSummary();

        await MessageService.Show(new MessageOption {
            Content = "Date filter cleared successfully",
            Color = Color.Info
        });
    }

    private Task<QueryData<MatlTran>> OnQueryAsync(QueryPageOptions options) {
        IEnumerable<MatlTran> items = Items;

        // Apply search
        if (SearchModel.TransNum > 0) {
            items = items.Where(i => i.TransNum == SearchModel.TransNum);
        }
        if (!string.IsNullOrEmpty(SearchModel.Item)) {
            items = items.Where(i => !string.IsNullOrEmpty(i.Item) && i.Item.Contains(SearchModel.Item, StringComparison.OrdinalIgnoreCase));
        }
        if (!string.IsNullOrEmpty(SearchModel.RefNum)) {
            items = items.Where(i => !string.IsNullOrEmpty(i.RefNum) && i.RefNum.Contains(SearchModel.RefNum, StringComparison.OrdinalIgnoreCase));
        }
        if (!string.IsNullOrEmpty(SearchModel.TransType)) {
            items = items.Where(i => !string.IsNullOrEmpty(i.TransType) && i.TransType.Contains(SearchModel.TransType, StringComparison.OrdinalIgnoreCase));
        }
        if (!string.IsNullOrEmpty(SearchModel.RefType)) {
            items = items.Where(i => !string.IsNullOrEmpty(i.RefType) && i.RefType.Contains(SearchModel.RefType, StringComparison.OrdinalIgnoreCase));
        }
        if (!string.IsNullOrEmpty(SearchModel.Loc)) {
            items = items.Where(i => !string.IsNullOrEmpty(i.Loc) && i.Loc.Contains(SearchModel.Loc, StringComparison.OrdinalIgnoreCase));
        }
        if (!string.IsNullOrEmpty(SearchModel.Lot)) {
            items = items.Where(i => !string.IsNullOrEmpty(i.Lot) && i.Lot.Contains(SearchModel.Lot, StringComparison.OrdinalIgnoreCase));
        }
        if (!string.IsNullOrEmpty(SearchModel.ReasonCode)) {
            items = items.Where(i => !string.IsNullOrEmpty(i.ReasonCode) && i.ReasonCode.Contains(SearchModel.ReasonCode, StringComparison.OrdinalIgnoreCase));
        }
        if (!string.IsNullOrEmpty(SearchModel.UM)) {
            items = items.Where(i => !string.IsNullOrEmpty(i.UM) && i.UM.Contains(SearchModel.UM, StringComparison.OrdinalIgnoreCase));
        }
        if (SearchModel.TransDate.HasValue) {
            var searchDate = SearchModel.TransDate.Value.Date;
            items = items.Where(i => i.TransDate.HasValue && i.TransDate.Value.Date == searchDate);
        }

        // Update filtered items for count display
        FilteredItems = items.ToList();

        // Apply sorting
        var isSorted = false;
        if (!string.IsNullOrEmpty(options.SortName)) {
            items = items.Sort(options.SortName, options.SortOrder);
            isSorted = true;
        }

        // Set the total record count
        var total = items.Count();

        // In-memory pagination
        items = items.Skip((options.PageIndex - 1) * options.PageItems).Take(options.PageItems);
        return Task.FromResult(new QueryData<MatlTran>() {
            Items = items,
            TotalCount = total,
            IsSorted = isSorted,
            IsFiltered = options.Filters.Count > 0,
            IsSearch = options.CustomerSearches.Count > 0 || !string.IsNullOrEmpty(options.SearchText),
            IsAdvanceSearch = options.CustomerSearches.Count > 0 && string.IsNullOrEmpty(options.SearchText),
        });
    }

    private Task OnResetSearchAsync(MatlTran searchModel) {
        searchModel.TransNum = 0;
        searchModel.Item = "";
        searchModel.RefNum = "";
        searchModel.TransType = "";
        searchModel.RefType = "";
        searchModel.Loc = "";
        searchModel.Lot = "";
        searchModel.ReasonCode = "";
        searchModel.UM = "";
        searchModel.TransDate = null;

        // Reset filtered items to all items
        FilteredItems = Items;
        return Task.CompletedTask;
    }

    private async Task RefreshData() {
        await LoadData();
        await LoadSummary();
        await MessageService.Show(new MessageOption {
            Content = "Data refreshed successfully",
            Color = Color.Success
        });
    }

    // Helper methods for safe formatting
    private string GetFormattedNumber(object? value, string format)
    {
        if (value == null) return "0";

        try
        {
            if (value is decimal decimalValue)
            {
                return decimalValue.ToString(format);
            }
            else if (decimal.TryParse(value.ToString(), out var parsedValue))
            {
                return parsedValue.ToString(format);
            }
        }
        catch
        {
            // Ignore formatting errors
        }

        return "0";
    }

    private string GetFormattedCurrency(object? value)
    {
        if (value == null) return "$0.00";

        try
        {
            if (value is decimal decimalValue)
            {
                return decimalValue.ToString("C");
            }
            else if (decimal.TryParse(value.ToString(), out var parsedValue))
            {
                return parsedValue.ToString("C");
            }
        }
        catch
        {
            // Ignore formatting errors
        }

        return "$0.00";
    }
}
