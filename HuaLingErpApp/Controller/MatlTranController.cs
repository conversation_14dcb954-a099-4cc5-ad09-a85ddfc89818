using Microsoft.AspNetCore.Mvc;
using HuaLingErpApp.Shared.Models;
using Microsoft.AspNetCore.Authorization;
using SqlSugar;

namespace HuaLingErpApp.Controller
{
    [ApiController]
    [Route("api/matltran")]
    [Authorize]
    public class MatlTranController(
        ILogger<MatlTranController> logger,
        ISqlSugarClient db)
        : ControllerBase
    {
        private readonly ISqlSugarClient _db = db.AsTenant().GetConnection("Default");

        /// <summary>
        /// Get all material transactions (read-only)
        /// </summary>
        /// <returns>List of material transactions</returns>
        [HttpGet]
        public async Task<ActionResult<HttpResponseModel<List<MatlTran>>>> GetAll()
        {
            try
            {
                var items = await _db.Queryable<MatlTran>()
                    .OrderByDescending(x => x.TransDate)
                    .OrderByDescending(x => x.TransNum)
                    .ToListAsync();

                return Ok(HttpResponseModel<List<MatlTran>>.Success(items));
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to retrieve material transactions");
                return StatusCode(500,
                    HttpResponseModel<List<MatlTran>>.Error($"An error occurred while retrieving material transactions: {ex.Message}"));
            }
        }

        /// <summary>
        /// Get material transactions by date range (read-only)
        /// </summary>
        /// <param name="startDate">Start date</param>
        /// <param name="endDate">End date</param>
        /// <returns>List of material transactions within date range</returns>
        [HttpGet("bydaterange")]
        public async Task<ActionResult<HttpResponseModel<List<MatlTran>>>> GetByDateRange(
            [FromQuery] DateTime? startDate, 
            [FromQuery] DateTime? endDate)
        {
            try
            {
                var query = _db.Queryable<MatlTran>();

                if (startDate.HasValue)
                {
                    query = query.Where(x => x.TransDate >= startDate.Value);
                }

                if (endDate.HasValue)
                {
                    // Include the entire end date (until 23:59:59)
                    var endOfDay = endDate.Value.Date.AddDays(1).AddTicks(-1);
                    query = query.Where(x => x.TransDate <= endOfDay);
                }

                var items = await query
                    .OrderByDescending(x => x.TransDate)
                    .OrderByDescending(x => x.TransNum)
                    .ToListAsync();

                return Ok(HttpResponseModel<List<MatlTran>>.Success(items));
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to retrieve material transactions by date range");
                return StatusCode(500,
                    HttpResponseModel<List<MatlTran>>.Error($"An error occurred while retrieving material transactions: {ex.Message}"));
            }
        }

        /// <summary>
        /// Get material transaction by ID (read-only)
        /// </summary>
        /// <param name="id">Transaction number</param>
        /// <returns>Material transaction</returns>
        [HttpGet("{id}")]
        public async Task<ActionResult<HttpResponseModel<MatlTran>>> GetById(int id)
        {
            try
            {
                var item = await _db.Queryable<MatlTran>()
                    .Where(x => x.TransNum == id)
                    .FirstAsync();

                if (item == null)
                {
                    return NotFound(HttpResponseModel<MatlTran>.Error("Material transaction not found"));
                }

                return Ok(HttpResponseModel<MatlTran>.Success(item));
            }
            catch (Exception ex)
            {
                logger.LogError(ex, $"Failed to retrieve material transaction with ID: {id}");
                return StatusCode(500,
                    HttpResponseModel<MatlTran>.Error($"An error occurred while retrieving the material transaction: {ex.Message}"));
            }
        }

        /// <summary>
        /// Get material transactions by item (read-only)
        /// </summary>
        /// <param name="item">Item code</param>
        /// <returns>List of material transactions for the item</returns>
        [HttpGet("byitem/{item}")]
        public async Task<ActionResult<HttpResponseModel<List<MatlTran>>>> GetByItem(string item)
        {
            try
            {
                var items = await _db.Queryable<MatlTran>()
                    .Where(x => x.Item == item)
                    .OrderByDescending(x => x.TransDate)
                    .OrderByDescending(x => x.TransNum)
                    .ToListAsync();

                return Ok(HttpResponseModel<List<MatlTran>>.Success(items));
            }
            catch (Exception ex)
            {
                logger.LogError(ex, $"Failed to retrieve material transactions for Item: {item}");
                return StatusCode(500,
                    HttpResponseModel<List<MatlTran>>.Error($"An error occurred while retrieving material transactions: {ex.Message}"));
            }
        }

        /// <summary>
        /// Get material transactions by location (read-only)
        /// </summary>
        /// <param name="loc">Location code</param>
        /// <returns>List of material transactions for the location</returns>
        [HttpGet("bylocation/{loc}")]
        public async Task<ActionResult<HttpResponseModel<List<MatlTran>>>> GetByLocation(string loc)
        {
            try
            {
                var items = await _db.Queryable<MatlTran>()
                    .Where(x => x.Loc == loc)
                    .OrderByDescending(x => x.TransDate)
                    .OrderByDescending(x => x.TransNum)
                    .ToListAsync();

                return Ok(HttpResponseModel<List<MatlTran>>.Success(items));
            }
            catch (Exception ex)
            {
                logger.LogError(ex, $"Failed to retrieve material transactions for Location: {loc}");
                return StatusCode(500,
                    HttpResponseModel<List<MatlTran>>.Error($"An error occurred while retrieving material transactions: {ex.Message}"));
            }
        }

        /// <summary>
        /// Get summary statistics (read-only)
        /// </summary>
        /// <param name="startDate">Start date for summary</param>
        /// <param name="endDate">End date for summary</param>
        /// <returns>Summary statistics</returns>
        [HttpGet("summary")]
        public async Task<ActionResult<HttpResponseModel<object>>> GetSummary(
            [FromQuery] DateTime? startDate, 
            [FromQuery] DateTime? endDate)
        {
            try
            {
                var query = _db.Queryable<MatlTran>();

                if (startDate.HasValue)
                {
                    query = query.Where(x => x.TransDate >= startDate.Value);
                }

                if (endDate.HasValue)
                {
                    var endOfDay = endDate.Value.Date.AddDays(1).AddTicks(-1);
                    query = query.Where(x => x.TransDate <= endOfDay);
                }

                var totalRecords = await query.CountAsync();
                var totalQty = await query.Where(x => x.Qty.HasValue).SumAsync(x => x.Qty.Value);
                var totalCost = await query.Where(x => x.Cost.HasValue).SumAsync(x => x.Cost.Value);
                var uniqueItems = await query.Where(x => !string.IsNullOrEmpty(x.Item))
                    .GroupBy(x => x.Item).CountAsync();
                var uniqueLocations = await query.Where(x => !string.IsNullOrEmpty(x.Loc))
                    .GroupBy(x => x.Loc).CountAsync();

                var summary = new
                {
                    TotalRecords = totalRecords,
                    TotalQuantity = totalQty,
                    TotalCost = totalCost,
                    UniqueItems = uniqueItems,
                    UniqueLocations = uniqueLocations,
                    DateRange = new
                    {
                        StartDate = startDate,
                        EndDate = endDate
                    }
                };

                return Ok(HttpResponseModel<object>.Success(summary));
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to retrieve material transactions summary");
                return StatusCode(500,
                    HttpResponseModel<object>.Error($"An error occurred while retrieving summary: {ex.Message}"));
            }
        }
    }
}
