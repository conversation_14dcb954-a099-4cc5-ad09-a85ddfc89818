2025-07-31 08:08:32 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-31 08:08:33 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-31 08:08:33 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-31 08:08:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-31 08:08:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-31 08:08:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-31 08:08:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-31 08:08:34 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-31 08:08:34 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-31 08:08:34 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-31 08:08:34 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-31 08:08:34 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-31 08:08:34 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-31 08:08:34 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-31 08:08:34 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-31 08:08:34 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-31 08:08:34 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-31 08:08:34 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-31 08:08:34 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-31 08:08:34 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-31 08:08:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2100:3e9af2bc successfully announced in 107.6546 ms
2025-07-31 08:08:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2100:a2e1f990 successfully announced in 107.6901 ms
2025-07-31 08:08:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2100:3e9af2bc is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-31 08:08:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2100:a2e1f990 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-31 08:08:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2100:3e9af2bc all the dispatchers started
2025-07-31 08:08:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2100:a2e1f990 all the dispatchers started
