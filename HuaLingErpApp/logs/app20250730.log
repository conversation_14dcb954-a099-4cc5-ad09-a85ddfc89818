2025-07-30 08:09:47 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-30 08:09:47 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-30 08:09:47 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-30 08:09:47 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-30 08:09:47 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-30 08:09:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-30 08:09:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-30 08:09:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-30 08:09:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-30 08:09:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-30 08:09:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-30 08:09:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-30 08:09:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-30 08:09:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-30 08:09:48 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-30 08:09:48 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-30 08:09:48 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-30 08:09:48 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-30 08:09:48 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-30 08:09:48 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-30 08:09:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14296:8087e0ad successfully announced in 191.3047 ms
2025-07-30 08:09:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14296:12f45e65 successfully announced in 167.4274 ms
2025-07-30 08:09:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14296:8087e0ad is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-30 08:09:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14296:12f45e65 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-30 08:09:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14296:12f45e65 all the dispatchers started
2025-07-30 08:09:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14296:8087e0ad all the dispatchers started
2025-07-30 09:35:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14296:8087e0ad caught stopping signal...
2025-07-30 09:35:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14296:12f45e65 caught stopping signal...
2025-07-30 09:35:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14296:8087e0ad All dispatchers stopped
2025-07-30 09:35:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14296:8087e0ad successfully reported itself as stopped in 2.4706 ms
2025-07-30 09:35:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14296:8087e0ad has been stopped in total 70.9279 ms
2025-07-30 09:35:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14296:12f45e65 caught stopped signal...
2025-07-30 09:35:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14296:12f45e65 All dispatchers stopped
2025-07-30 09:35:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14296:12f45e65 successfully reported itself as stopped in 0.8874 ms
2025-07-30 09:35:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14296:12f45e65 has been stopped in total 883.6532 ms
2025-07-30 09:35:45 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-30 09:35:45 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-30 09:35:45 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-30 09:35:45 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-30 09:35:45 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-30 09:35:46 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-30 09:35:46 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-30 09:35:46 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-30 09:35:46 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-30 09:35:46 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-30 09:35:46 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-30 09:35:46 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-30 09:35:46 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-30 09:35:46 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-30 09:35:46 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-30 09:35:46 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-30 09:35:46 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-30 09:35:46 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-30 09:35:46 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-30 09:35:46 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-30 09:35:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2152:499ac32b successfully announced in 107.8579 ms
2025-07-30 09:35:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2152:95042561 successfully announced in 107.8275 ms
2025-07-30 09:35:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2152:95042561 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-30 09:35:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2152:499ac32b is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-30 09:35:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2152:95042561 all the dispatchers started
2025-07-30 09:35:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2152:499ac32b all the dispatchers started
2025-07-30 09:48:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2152:499ac32b caught stopping signal...
2025-07-30 09:48:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2152:95042561 caught stopping signal...
2025-07-30 09:48:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2152:95042561 caught stopped signal...
2025-07-30 09:48:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2152:499ac32b caught stopped signal...
2025-07-30 09:48:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2152:499ac32b All dispatchers stopped
2025-07-30 09:48:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2152:95042561 All dispatchers stopped
2025-07-30 09:48:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2152:95042561 successfully reported itself as stopped in 2.401 ms
2025-07-30 09:48:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2152:95042561 has been stopped in total 975.3287 ms
2025-07-30 09:48:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2152:499ac32b successfully reported itself as stopped in 1.6788 ms
2025-07-30 09:48:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2152:499ac32b has been stopped in total 976.2762 ms
2025-07-30 10:04:33 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-30 10:04:33 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-30 10:04:33 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-30 10:04:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-30 10:04:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-30 10:04:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-30 10:04:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-30 10:04:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-30 10:04:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-30 10:04:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-30 10:04:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-30 10:04:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-30 10:04:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-30 10:04:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-30 10:04:34 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-30 10:04:34 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-30 10:04:34 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-30 10:04:34 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-30 10:04:34 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-30 10:04:34 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-30 10:04:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16552:c2d5a295 successfully announced in 104.3459 ms
2025-07-30 10:04:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16552:7d84a50a successfully announced in 103.6919 ms
2025-07-30 10:04:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16552:c2d5a295 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-30 10:04:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16552:7d84a50a is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-30 10:04:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16552:c2d5a295 all the dispatchers started
2025-07-30 10:04:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16552:7d84a50a all the dispatchers started
2025-07-30 10:13:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16552:7d84a50a caught stopping signal...
2025-07-30 10:13:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16552:c2d5a295 caught stopping signal...
2025-07-30 10:13:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16552:c2d5a295 All dispatchers stopped
2025-07-30 10:13:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16552:c2d5a295 successfully reported itself as stopped in 1.9474 ms
2025-07-30 10:13:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16552:c2d5a295 has been stopped in total 44.872 ms
2025-07-30 10:13:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16552:7d84a50a caught stopped signal...
2025-07-30 10:13:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16552:7d84a50a All dispatchers stopped
2025-07-30 10:13:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16552:7d84a50a successfully reported itself as stopped in 0.9229 ms
2025-07-30 10:13:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16552:7d84a50a has been stopped in total 959.3708 ms
