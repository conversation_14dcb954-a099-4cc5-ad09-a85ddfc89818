2025-07-30 08:09:47 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-30 08:09:47 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-30 08:09:47 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-30 08:09:47 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-30 08:09:47 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-30 08:09:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-30 08:09:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-30 08:09:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-30 08:09:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-30 08:09:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-30 08:09:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-30 08:09:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-30 08:09:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-30 08:09:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-30 08:09:48 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-30 08:09:48 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-30 08:09:48 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-30 08:09:48 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-30 08:09:48 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-30 08:09:48 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-30 08:09:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14296:8087e0ad successfully announced in 191.3047 ms
2025-07-30 08:09:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14296:12f45e65 successfully announced in 167.4274 ms
2025-07-30 08:09:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14296:8087e0ad is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-30 08:09:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14296:12f45e65 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-30 08:09:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14296:12f45e65 all the dispatchers started
2025-07-30 08:09:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14296:8087e0ad all the dispatchers started
2025-07-30 09:35:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14296:8087e0ad caught stopping signal...
2025-07-30 09:35:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14296:12f45e65 caught stopping signal...
2025-07-30 09:35:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14296:8087e0ad All dispatchers stopped
2025-07-30 09:35:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14296:8087e0ad successfully reported itself as stopped in 2.4706 ms
2025-07-30 09:35:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14296:8087e0ad has been stopped in total 70.9279 ms
2025-07-30 09:35:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14296:12f45e65 caught stopped signal...
2025-07-30 09:35:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14296:12f45e65 All dispatchers stopped
2025-07-30 09:35:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14296:12f45e65 successfully reported itself as stopped in 0.8874 ms
2025-07-30 09:35:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14296:12f45e65 has been stopped in total 883.6532 ms
2025-07-30 09:35:45 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-30 09:35:45 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-30 09:35:45 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-30 09:35:45 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-30 09:35:45 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-30 09:35:46 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-30 09:35:46 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-30 09:35:46 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-30 09:35:46 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-30 09:35:46 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-30 09:35:46 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-30 09:35:46 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-30 09:35:46 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-30 09:35:46 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-30 09:35:46 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-30 09:35:46 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-30 09:35:46 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-30 09:35:46 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-30 09:35:46 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-30 09:35:46 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-30 09:35:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2152:499ac32b successfully announced in 107.8579 ms
2025-07-30 09:35:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2152:95042561 successfully announced in 107.8275 ms
2025-07-30 09:35:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2152:95042561 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-30 09:35:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2152:499ac32b is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-30 09:35:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2152:95042561 all the dispatchers started
2025-07-30 09:35:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2152:499ac32b all the dispatchers started
2025-07-30 09:48:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2152:499ac32b caught stopping signal...
2025-07-30 09:48:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2152:95042561 caught stopping signal...
2025-07-30 09:48:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2152:95042561 caught stopped signal...
2025-07-30 09:48:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2152:499ac32b caught stopped signal...
2025-07-30 09:48:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2152:499ac32b All dispatchers stopped
2025-07-30 09:48:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2152:95042561 All dispatchers stopped
2025-07-30 09:48:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2152:95042561 successfully reported itself as stopped in 2.401 ms
2025-07-30 09:48:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2152:95042561 has been stopped in total 975.3287 ms
2025-07-30 09:48:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2152:499ac32b successfully reported itself as stopped in 1.6788 ms
2025-07-30 09:48:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2152:499ac32b has been stopped in total 976.2762 ms
2025-07-30 10:04:33 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-30 10:04:33 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-30 10:04:33 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-30 10:04:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-30 10:04:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-30 10:04:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-30 10:04:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-30 10:04:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-30 10:04:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-30 10:04:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-30 10:04:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-30 10:04:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-30 10:04:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-30 10:04:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-30 10:04:34 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-30 10:04:34 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-30 10:04:34 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-30 10:04:34 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-30 10:04:34 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-30 10:04:34 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-30 10:04:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16552:c2d5a295 successfully announced in 104.3459 ms
2025-07-30 10:04:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16552:7d84a50a successfully announced in 103.6919 ms
2025-07-30 10:04:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16552:c2d5a295 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-30 10:04:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16552:7d84a50a is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-30 10:04:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16552:c2d5a295 all the dispatchers started
2025-07-30 10:04:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16552:7d84a50a all the dispatchers started
2025-07-30 10:13:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16552:7d84a50a caught stopping signal...
2025-07-30 10:13:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16552:c2d5a295 caught stopping signal...
2025-07-30 10:13:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16552:c2d5a295 All dispatchers stopped
2025-07-30 10:13:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16552:c2d5a295 successfully reported itself as stopped in 1.9474 ms
2025-07-30 10:13:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16552:c2d5a295 has been stopped in total 44.872 ms
2025-07-30 10:13:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16552:7d84a50a caught stopped signal...
2025-07-30 10:13:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16552:7d84a50a All dispatchers stopped
2025-07-30 10:13:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16552:7d84a50a successfully reported itself as stopped in 0.9229 ms
2025-07-30 10:13:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16552:7d84a50a has been stopped in total 959.3708 ms
2025-07-30 10:14:05 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-30 10:14:05 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-30 10:14:05 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-30 10:14:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-30 10:14:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-30 10:14:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-30 10:14:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-30 10:14:06 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-30 10:14:06 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-30 10:14:06 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-30 10:14:06 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-30 10:14:06 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-30 10:14:06 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-30 10:14:06 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-30 10:14:06 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-30 10:14:06 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-30 10:14:06 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-30 10:14:06 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-30 10:14:06 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-30 10:14:06 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-30 10:14:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17184:8ee25579 successfully announced in 104.6937 ms
2025-07-30 10:14:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17184:279c2a10 successfully announced in 104.8879 ms
2025-07-30 10:14:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17184:8ee25579 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-30 10:14:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17184:279c2a10 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-30 10:14:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17184:279c2a10 all the dispatchers started
2025-07-30 10:14:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17184:8ee25579 all the dispatchers started
2025-07-30 10:17:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17184:279c2a10 caught stopping signal...
2025-07-30 10:17:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17184:8ee25579 caught stopping signal...
2025-07-30 10:17:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17184:8ee25579 caught stopped signal...
2025-07-30 10:17:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17184:279c2a10 caught stopped signal...
2025-07-30 10:17:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17184:8ee25579 All dispatchers stopped
2025-07-30 10:17:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17184:279c2a10 All dispatchers stopped
2025-07-30 10:17:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17184:8ee25579 successfully reported itself as stopped in 1.8503 ms
2025-07-30 10:17:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17184:8ee25579 has been stopped in total 569.6725 ms
2025-07-30 10:17:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17184:279c2a10 successfully reported itself as stopped in 1.3604 ms
2025-07-30 10:17:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17184:279c2a10 has been stopped in total 570.8296 ms
2025-07-30 10:18:08 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-30 10:18:08 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-30 10:18:08 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-30 10:18:08 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-30 10:18:08 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-30 10:18:08 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-30 10:18:08 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-30 10:18:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-30 10:18:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-30 10:18:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-30 10:18:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-30 10:18:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-30 10:18:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-30 10:18:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-30 10:18:09 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-30 10:18:09 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-30 10:18:09 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-30 10:18:09 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-30 10:18:09 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-30 10:18:09 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-30 10:18:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20372:47401c07 successfully announced in 116.7541 ms
2025-07-30 10:18:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20372:5504e552 successfully announced in 117.2033 ms
2025-07-30 10:18:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20372:5504e552 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-30 10:18:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20372:47401c07 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-30 10:18:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20372:5504e552 all the dispatchers started
2025-07-30 10:18:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20372:47401c07 all the dispatchers started
2025-07-30 10:24:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20372:5504e552 caught stopping signal...
2025-07-30 10:24:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20372:47401c07 caught stopping signal...
2025-07-30 10:24:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20372:5504e552 All dispatchers stopped
2025-07-30 10:24:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20372:5504e552 successfully reported itself as stopped in 1.8869 ms
2025-07-30 10:24:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20372:5504e552 has been stopped in total 374.1453 ms
2025-07-30 10:24:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20372:47401c07 All dispatchers stopped
2025-07-30 10:24:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20372:47401c07 successfully reported itself as stopped in 0.8086 ms
2025-07-30 10:24:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20372:47401c07 has been stopped in total 600.71 ms
2025-07-30 10:24:34 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-30 10:24:34 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-30 10:24:34 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-30 10:24:34 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-30 10:24:34 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-30 10:24:34 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-30 10:24:34 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-30 10:24:35 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-30 10:24:35 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-30 10:24:35 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-30 10:24:35 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-30 10:24:35 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-30 10:24:35 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-30 10:24:35 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-30 10:24:35 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-30 10:24:35 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-30 10:24:35 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-30 10:24:35 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-30 10:24:35 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-30 10:24:35 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-30 10:24:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11976:c6580925 successfully announced in 114.7679 ms
2025-07-30 10:24:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11976:9404474a successfully announced in 114.6675 ms
2025-07-30 10:24:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11976:c6580925 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-30 10:24:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11976:9404474a is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-30 10:24:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11976:9404474a all the dispatchers started
2025-07-30 10:24:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11976:c6580925 all the dispatchers started
2025-07-30 10:25:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11976:9404474a caught stopping signal...
2025-07-30 10:25:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11976:c6580925 caught stopping signal...
2025-07-30 10:25:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11976:c6580925 caught stopped signal...
2025-07-30 10:25:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11976:9404474a caught stopped signal...
2025-07-30 10:25:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11976:9404474a All dispatchers stopped
2025-07-30 10:25:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11976:c6580925 All dispatchers stopped
2025-07-30 10:25:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11976:c6580925 successfully reported itself as stopped in 2.0335 ms
2025-07-30 10:25:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11976:c6580925 has been stopped in total 743.7296 ms
2025-07-30 10:25:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11976:9404474a successfully reported itself as stopped in 1.4787 ms
2025-07-30 10:25:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11976:9404474a has been stopped in total 745.4231 ms
2025-07-30 10:25:53 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-30 10:25:53 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-30 10:25:53 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-30 10:25:53 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-30 10:25:53 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-30 10:25:53 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-30 10:25:53 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-30 10:25:54 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-30 10:25:54 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-30 10:25:54 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-30 10:25:54 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-30 10:25:54 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-30 10:25:54 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-30 10:25:54 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-30 10:25:54 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-30 10:25:54 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-30 10:25:54 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-30 10:25:54 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-30 10:25:54 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-30 10:25:54 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-30 10:25:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12688:f5051885 successfully announced in 102.7118 ms
2025-07-30 10:25:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12688:c2fcb0db successfully announced in 102.7202 ms
2025-07-30 10:25:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12688:f5051885 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-30 10:25:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12688:c2fcb0db is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-30 10:25:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12688:f5051885 all the dispatchers started
2025-07-30 10:25:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12688:c2fcb0db all the dispatchers started
2025-07-30 10:26:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12688:f5051885 caught stopping signal...
2025-07-30 10:26:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12688:c2fcb0db caught stopping signal...
2025-07-30 10:26:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12688:c2fcb0db All dispatchers stopped
2025-07-30 10:26:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12688:f5051885 All dispatchers stopped
2025-07-30 10:26:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12688:c2fcb0db successfully reported itself as stopped in 0.8485 ms
2025-07-30 10:26:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12688:c2fcb0db has been stopped in total 460.9908 ms
2025-07-30 10:26:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12688:f5051885 successfully reported itself as stopped in 2.2068 ms
2025-07-30 10:26:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12688:f5051885 has been stopped in total 465.539 ms
